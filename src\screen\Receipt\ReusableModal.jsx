import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Modal,
} from 'react-native';
import { Calendar } from 'react-native-calendars';

/**
 * ReceiptModals Component
 * 
 * Contains all modal components used in the Receipt for Subscription screen.
 * Includes Selection Modal, Customer Modal, Calendar Modal, and DocName Modal.
 */
const ReceiptModals = ({
    // Selection Modal Props
    modalVisible,
    setModalVisible,
    modalType,
    modalData,
    filteredModalData,
    searchTerm,
    setSearchTerm,
    isLoadingItems,
    handleModalItemSelect,
    
    // Customer Modal Props
    customerModalVisible,
    setCustomerModalVisible,
    customerSearchTerm,
    setCustomerSearchTerm,
    filteredCustomers,
    handleCustomerSelect,
    handleCustomerSearch,
    isMobileSearch, // Add this
    mobile, // Add this
    
    // Calendar Modal Props
    calendarVisible,
    setCalendarVisible,
    handleDateSelect,
    convertDateForCalendar,
    selectedDateField,
    businessDate,
    chequeDate,
    
    // DocName Modal Props
    docNameModalVisible,
    setDocNameModalVisible,
    docNameSearchTerm,
    setDocNameSearchTerm,
    filteredDocNames,
    isLoadingDocNames,
    handleDocNameSelect,
}) => {
    
    return (
        <>
            {/* Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'receiptMode' ? 'Select Receipt Mode' : 
                             modalType === 'itemName' ? 'Select Item' : 'Select Option'}
                        </Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {isLoadingItems ? (
                                    <View style={styles.loadingContainer}>
                                        <Text style={styles.loadingText}>Loading items...</Text>
                                    </View>
                                ) : (
                                    filteredModalData.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.modalItem}
                                            onPress={() => handleModalItemSelect(item)}
                                        >
                                            <Text style={styles.modalItemText}>
                                                {modalType === 'itemName' ? item.itemName : item}
                                            </Text>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Customer Selection Modal */}
{/* Customer Selection Modal */}
<Modal
    animationType="fade"
    transparent={true}
    visible={customerModalVisible}
    onRequestClose={() => setCustomerModalVisible(false)}
>
    <TouchableOpacity 
        style={styles.modalOverlay} 
        activeOpacity={1} 
        onPress={() => setCustomerModalVisible(false)}
    >
        <View style={styles.modalContent}>
        <Text style={styles.modalTitle}>
    {isMobileSearch ? `Select Customer (Mobile: ${mobile})` : 'Select Customer'}
</Text>
            
            <View style={styles.customerSearchContainer}>
                <TextInput
                    style={styles.customerSearchInput}
                    placeholder="Search Customer by Name"
                    placeholderTextColor="#888"
                    value={customerSearchTerm}
                    onChangeText={setCustomerSearchTerm}
                />
                <TouchableOpacity 
                    style={styles.searchButton}
                    onPress={handleCustomerSearch}
                >
                    <Text style={styles.searchButtonText}>Search</Text>
                </TouchableOpacity>
            </View>
            
            <ScrollView>
                <View style={styles.customerItemsContainer}>
                    {filteredCustomers.length > 0 ? (
                        filteredCustomers.map((customer, index) => (
                            <TouchableOpacity
                                key={index}
                                style={styles.customerItem}
                                onPress={() => handleCustomerSelect(customer)}
                            >
                                <Text style={styles.customerItemText}>
                                    {customer.customerName}
                                </Text>
                                <Text style={styles.customerMobileText}>
                                    {customer.mobile}
                                </Text>
                            </TouchableOpacity>
                        ))
                    ) : (
                        <View style={styles.noDataContainer}>
                            <Text style={styles.noDataText}>
                                {customerSearchTerm ? 'No customers found' : 'Type to search customers'}
                            </Text>
                        </View>
                    )}
                </View>
            </ScrollView>
            
            <TouchableOpacity 
                style={styles.modalCloseButton}
                onPress={() => setCustomerModalVisible(false)}
            >
                <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
        </View>
    </TouchableOpacity>
</Modal>

            {/* Calendar Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={calendarVisible}
                onRequestClose={() => setCalendarVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setCalendarVisible(false)}
                >
                    <View style={styles.calendarModalContent}>
                        <Text style={styles.modalTitle}>Select Date</Text>
                        
                        <Calendar
                            onDayPress={handleDateSelect}
                            markedDates={{
                                [convertDateForCalendar(selectedDateField === 'businessDate' ? businessDate : chequeDate)]: {
                                    selected: true,
                                    selectedColor: '#FDC500'
                                }
                            }}
                            theme={{
                                backgroundColor: '#ffffff',
                                calendarBackground: '#ffffff',
                                textSectionTitleColor: '#02096A',
                                selectedDayBackgroundColor: '#FDC500',
                                selectedDayTextColor: '#000000',
                                todayTextColor: '#02096A',
                                dayTextColor: '#2d4150',
                                textDisabledColor: '#d9e1e8',
                                dotColor: '#FDC500',
                                selectedDotColor: '#ffffff',
                                arrowColor: '#02096A',
                                disabledArrowColor: '#d9e1e8',
                                monthTextColor: '#02096A',
                                indicatorColor: '#02096A',
                                textDayFontFamily: 'Poppins',
                                textMonthFontFamily: 'Poppins',
                                textDayHeaderFontFamily: 'Poppins',
                                textDayFontWeight: 'bold',
                                textMonthFontWeight: 'bold',
                                textDayHeaderFontWeight: 'bold',
                                textDayFontSize: 14,
                                textMonthFontSize: 16,
                                textDayHeaderFontSize: 12
                            }}
                        />
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCalendarVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* DocName Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={docNameModalVisible}
                onRequestClose={() => setDocNameModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setDocNameModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Select Document</Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search Document"
                                placeholderTextColor="#888"
                                value={docNameSearchTerm}
                                onChangeText={setDocNameSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {isLoadingDocNames ? (
                                    <View style={styles.loadingContainer}>
                                        <Text style={styles.loadingText}>Loading documents...</Text>
                                    </View>
                                ) : (
                                    filteredDocNames.map((item, index) => (
                                        <TouchableOpacity
                                            key={index}
                                            style={styles.modalItem}
                                            onPress={() => handleDocNameSelect(item)}
                                        >
                                            <Text style={styles.modalItemText}>
                                                {item.DocName}
                                            </Text>
                                        </TouchableOpacity>
                                    ))
                                )}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setDocNameModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
            {isMobileSearch && filteredCustomers.length > 1 && (
    <Text style={styles.customerCountText}>
        {filteredCustomers.length} customers found with this mobile number
    </Text>
)}
        </>
    );
};

const styles = StyleSheet.create({
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    // Add these styles to your existing styles object
customerMobileText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'Poppins',
    textAlign: 'center',
    marginTop: 2,
},
noDataContainer: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
},
noDataText: {
    fontSize: 14,
    color: '#888',
    fontFamily: 'Poppins',
    fontStyle: 'italic',
    textAlign: 'center',
},
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    // Loading styles
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        fontSize: 16,
        color: '#888',
        fontFamily: 'Poppins',
        fontStyle: 'italic',
    },
    // Customer Modal Styles
    customerSearchContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerSearchInput: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    searchButton: {
        backgroundColor: '#02096A',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    searchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        fontSize: 14,
    },
    customerItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
// Update the existing customerItem style
customerItem: {
    backgroundColor: '#FDC500',
    width: 140,
    height: 80, // Increased height to accommodate mobile number
    padding: 10,
    borderRadius: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 5,
},
    customerItemText: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    // Calendar Modal Styles
    calendarModalContent: {
        width: '90%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
});

export default ReceiptModals;