import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Modal,
    FlatList,
    ActivityIndicator,
    TouchableWithoutFeedback,
    Keyboard,
} from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import Navbar from '../../components/Navbar';
import axios from 'axios';

const OperationAttendanceScreen = () => {
    const [remarks, setRemarks] = useState('');
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [modalVisible, setModalVisible] = useState(false);
    const [branchSearch, setBranchSearch] = useState('');
    const [branches, setBranches] = useState([]);
    const [filteredBranches, setFilteredBranches] = useState([]);
    const [loading, setLoading] = useState(false);
    const navigation = useNavigation();
    const [selectedBranchName, setSelectedBranchName] = useState('');


    const fetchBranches = async () => {
        try {
            setLoading(true);
            const bearerToken = await AsyncStorage.getItem('authToken');
            const userDataString = await AsyncStorage.getItem('userData');
            const userData = JSON.parse(userDataString);
            const employeeId = userData?.userId;

            const response = await axios.get(
                `https://retailuat.abisibg.com/api/v1/branchaccess?EmpId=${employeeId}`,
                {
                    headers: {
                        Authorization: `Bearer ${bearerToken}`,
                    },
                }
            );

            const branches = response.data;
            if (Array.isArray(branches) && branches.length > 0) {
                const branchNames = branches.map(item => item.BranchName);
                setBranches(branchNames);
                setFilteredBranches(branchNames);
            } else {
                alert('No branches found.');
            }
        } catch (error) {
            console.error('Fetch branches error:', error?.response?.data || error.message);
            alert('Error fetching branches');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const filtered = branchSearch.trim() === ''
            ? branches
            : branches.filter(branch =>
                branch.toLowerCase().includes(branchSearch.toLowerCase())
            );
        setFilteredBranches(filtered);
    }, [branchSearch, branches]);


    const handleSelectBranch = (branch) => {
        setSelectedBranchName(branch); // Show branch in "Name" input
        setModalVisible(false);
        setBranchSearch('');
        setFilteredBranches([]);
    };

    return (
        <ScrollView style={styles.container}>
            <Navbar />

            <View style={styles.headerRow}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Icon name="arrow-left" size={20} color="#000" />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>ATTENDANCE (OPENING)</Text>

                <View style={styles.actionButtons}>
                    <TouchableOpacity style={styles.newBtn}>
                        <Icon name="user-plus" size={16} color="#000" />
                        <Text style={styles.actionText}> New</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.viewBtn}>
                        <Icon name="eye" size={16} color="#000" />
                        <Text style={styles.actionText}> View</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.saveBtn}>
                        <Text style={styles.saveText}>Save</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.cancelBtn}>
                        <Text style={styles.cancelText}>Cancel</Text>
                    </TouchableOpacity>
                </View>
            </View>

            <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                    <TextInput placeholder="Approved By" style={styles.inputBox} />
                    <TouchableOpacity
                        style={styles.searchBtn}
                        onPress={() => {
                            setModalVisible(true);
                            fetchBranches();
                        }}
                    >
                        <Text style={styles.searchText}>Search</Text>
                    </TouchableOpacity>
                    <TextInput
                        placeholder="Name"
                        style={styles.inputBox}
                        value={selectedBranchName}
                        onChangeText={setSelectedBranchName}
                    />
                </View>

                <View style={styles.remarksBox}>
                    <TextInput
                        style={styles.remarksInput}
                        placeholder="Add Remarks"
                        multiline
                        numberOfLines={4}
                        value={remarks}
                        onChangeText={setRemarks}
                    />
                </View>
            </View>

            <View style={styles.buttonGrid}>
                {['Attendance In', 'Attendance Out', 'Leave', 'Absent', 'CompOff', 'Week Off', 'ESIC Lv'].map((label, idx) => (
                    <TouchableOpacity
                        key={idx}
                        onPress={() => setSelectedIndex(idx)}
                        style={[
                            styles.yellowBtn,
                            selectedIndex === idx && styles.selectedBtn,
                        ]}
                    >
                        <Text style={styles.btnText}>{label}</Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Modal */}
            <Modal
                visible={modalVisible}
                transparent
                animationType="slide"
                onRequestClose={() => {
                    setModalVisible(false);
                    setBranchSearch('');
                    setFilteredBranches([]);
                }}
            >
                <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
                    <View style={styles.modalOverlay}>
                        <View style={styles.modalContainer}>
                            <Text style={styles.modalTitle}>Search Branch</Text>

                            <View style={styles.modalSearchRow}>
                                <TextInput
                                    placeholder="Enter branch name"
                                    style={styles.modalInput}
                                    value={branchSearch}
                                    onChangeText={setBranchSearch}
                                />
                            </View>

                            {loading ? (
                                <ActivityIndicator size="large" color="#001F54" />
                            ) : filteredBranches.length === 0 ? (
                                <Text style={{ textAlign: 'center', marginTop: 20, marginBottom: 20, color: 'gray' }}>
                                    No branches found.
                                </Text>
                            ) : (
                                <FlatList
                                    data={filteredBranches}
                                    keyExtractor={(item, index) => index.toString()}
                                    numColumns={4}
                                    contentContainerStyle={styles.cardContainer}
                                    columnWrapperStyle={{ justifyContent: 'space-between' }}
                                    renderItem={({ item }) => (
                                        <TouchableOpacity
                                            onPress={() => handleSelectBranch(item)}
                                            style={styles.branchCard}
                                        >
                                            <Text style={styles.branchCardText}>{item}</Text>
                                        </TouchableOpacity>
                                    )}
                                />
                            )}

                            <TouchableOpacity
                                style={[styles.cancelBtn, { alignSelf: 'center', marginTop: 10 }]}
                                onPress={() => {
                                    setModalVisible(false);
                                    setBranchSearch('');
                                    setFilteredBranches([]);
                                }}
                            >
                                <Text style={styles.cancelText}>Close</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>

        </ScrollView>
    );
};

export default OperationAttendanceScreen;


const styles = StyleSheet.create({
    branchCard: {
        backgroundColor: '#FDC500',
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        width: '23%', // 4 cards in one row with spacing
        aspectRatio: 1, // ensures the card is square (width = height)
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
        elevation: 4,
    },

    branchCardText: {
        textAlign: 'center',
        fontSize: 14,
        fontWeight: '600',
        color: '#001F54',
    },

    cardContainer: {
        paddingHorizontal: 3,
        paddingBottom: 20,
    },

    container: {
        flex: 1,
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#EBEBEB',
        padding: 20,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },
    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,

    },
    inputContainer: {
        backgroundColor: '#EBEBEB',
        marginTop: 10,
    },
    inputRow: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: 10,
        flexWrap: 'wrap',
        gap: 10,
    },
    inputBox: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 16,
        paddingVertical: 14,
        fontSize: 16,
        backgroundColor: 'white',
        marginRight: 5,
    },
    searchBtn: {
        backgroundColor: '#001F54',
        paddingVertical: 14,
        paddingHorizontal: 20,
        borderRadius: 8,
    },
    searchText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    remarksBox: {
        margin: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        borderRadius: 8,
        backgroundColor: '#fff',
        width: '60%',
        alignSelf: 'center',
    },
    remarksInput: {
        fontSize: 16,
        color: '#333',
        textAlignVertical: 'top',
        minHeight: 100,
    },
    buttonGrid: {
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: 10,
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    yellowBtn: {
        backgroundColor: '#FFC107',
        paddingVertical: 14,
        paddingHorizontal: 30,
        margin: 6,
        borderRadius: 30,
    },
    selectedBtn: {
        borderWidth: 2,
        borderColor: '#001F54',
    },
    btnText: {
        color: '#000',
        fontWeight: '700',
        fontSize: 16,
    },
    modalOverlay: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContainer: {
        backgroundColor: 'white',
        marginHorizontal: 20,
        borderRadius: 10,
        padding: 20,
        maxHeight: '80%',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        textAlign: 'center',
        alignSelf: 'center',
    },
    modalSearchRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        marginBottom: 10,
    },
    modalInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        paddingVertical: 12,
        fontSize: 16,
    },
    modalSearchBtn: {
        backgroundColor: '#001F54',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 8,
    },
    branchItem: {
        padding: 12,
        fontSize: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
});

