// src/navigation/AppNavigator.js
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import SplashScreen from '../login/SplashScreen';
import LoginScreen from '../login/LoginScreen';
import Navbar from '../components/Navbar';
// import ReceivingScreen from '../screen/stock/ReceivingScreen';
import BillingScreen from '../screen/billing/BillingScreen';
import MainNavbar from '../components/MainNavbar';
import BillingPaymentScreen from '../screen/billing/BillingPaymentScreen';
import ViewBillScreen from '../screen/billing/ViewBillScreen';


const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="Splash" screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Main" component={MainNavbar} /> 
      {/* <Stack.Screen name="Receiving" component={ReceivingScreen} />  */}
      <Stack.Screen name="Billing" component={BillingScreen} />  
      <Stack.Screen name="BillingPayment" component={BillingPaymentScreen} />
      <Stack.Screen name="ViewBillScreen" component={ViewBillScreen} />
      
    </Stack.Navigator>
  );
};

export default AppNavigator;
