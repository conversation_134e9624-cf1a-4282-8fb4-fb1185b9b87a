import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  ScrollView,
} from 'react-native';
import {
  Layout,
  Text,
  Input,
  Button,
  Select,
  SelectItem,
  Datepicker,
  IndexPath,
} from '@ui-kitten/components';
import Navbar from '../../components/Navbar';

const LeaveScreen = () => {
  const [employeeName, setEmployeeName] = useState('');
  const [selectedAllocation, setSelectedAllocation] = useState(new IndexPath(0));
  const [fromDate, setFromDate] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [remarks, setRemarks] = useState('');
  const [items, setItems] = useState([]);

  const allocationOptions = ['Paid Leave', 'Sick Leave', 'Casual Leave'];

  const handleAdd = () => {
    setItems([
      ...items,
      {
        id: Date.now().toString(),
        lineNumber: items.length + 1,
        itemName: allocationOptions[selectedAllocation.row],
        nos: 1,
        kgs: 0,
        remarks: remarks || '',
      },
    ]);
    setRemarks('');
  };

  const handleDelete = (id) => {
    setItems(items.filter(item => item.id !== id));
  };

  const renderItem = ({ item }) => (
    <Layout style={styles.row}>
      <Text style={styles.cell}>{item.lineNumber}</Text>
      <Text style={styles.cell}>{item.itemName}</Text>
      <Text style={styles.cell}>{item.nos}</Text>
      <Text style={styles.cell}>{item.kgs}</Text>
      <Text style={styles.cell}>{item.remarks}</Text>
      <Button size="tiny" status="danger" onPress={() => handleDelete(item.id)}>
        Delete
      </Button>
    </Layout>
  );

  return (
     <ScrollView style={styles.container}>
        <Navbar />
    <Layout style={styles.container}>
    
      <Text category="h5" style={styles.heading}>LEAVE</Text>

      <Layout style={styles.rowBetween}>
        <Input
          placeholder="Employee Name"
          value={employeeName}
          onChangeText={setEmployeeName}
          style={styles.input}
        />
        <Select
          selectedIndex={selectedAllocation}
          onSelect={index => setSelectedAllocation(index)}
          style={styles.input}
          value={allocationOptions[selectedAllocation.row]}
        >
          {allocationOptions.map((title, idx) => (
            <SelectItem title={title} key={idx} />
          ))}
        </Select>
      </Layout>

      <Layout style={styles.rowBetween}>
        <Datepicker
          date={fromDate}
          onSelect={setFromDate}
          style={styles.input}
          placeholder="From Date"
        />
        <Datepicker
          date={toDate}
          onSelect={setToDate}
          style={styles.input}
          placeholder="To Date"
        />
      </Layout>

      <Input
        placeholder="Add Remarks"
        value={remarks}
        onChangeText={setRemarks}
        multiline
        textStyle={{ minHeight: 64 }}
        style={styles.fullInput}
      />

      <Layout style={styles.rowBetween}>
        <Button status="success" onPress={handleAdd} style={styles.button}>
          Add
        </Button>
        <Button status="danger" onPress={() => setItems([])} style={styles.button}>
          Clear
        </Button>
      </Layout>

      <Layout style={styles.tableHeader}>
        <Text style={styles.cell}>Line#</Text>
        <Text style={styles.cell}>Item</Text>
        <Text style={styles.cell}>Nos</Text>
        <Text style={styles.cell}>Kgs</Text>
        <Text style={styles.cell}>Remarks</Text>
        <Text style={styles.cell}>Action</Text>
      </Layout>

      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        style={styles.list}
      />
    </Layout>
     </ScrollView>
    
  );
};

export default LeaveScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 16,
  },
  heading: {
    marginBottom: 8,
    fontWeight: 'bold',
    
  },
  input: {
    flex: 1,
    margin: 4,
  },
  fullInput: {
    marginVertical: 10,
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    margin: 4,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#ddd',
    padding: 8,
    marginTop: 16,
  },
  row: {
    flexDirection: 'row',
    padding: 8,
    borderBottomWidth: 1,
    borderColor: '#ccc',
    alignItems: 'center',
  },
  cell: {
    flex: 1,
    fontSize: 12,
    marginHorizontal: 2,
  },
  list: {
    maxHeight: 300,
    marginTop: 8,
  },
});
