import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import ReceivingScreen from '../screen/stock/ReceivingScreen';
import BillingScreen from '../screen/billing/BillingScreen';
import { TripScreen } from "../screen/logistics/Trips/TripScreen";
import { TripPlan } from "../screen/logistics/TripPlan/TripPlan";
import { TripExpenses } from "../screen/logistics/TripExpenses/TripExpenses";
import { TripDelivery } from "../screen/logistics/TripDelivery/TripDelivery";
import { ReceiptForSubscription } from "../screen/Receipt/ReceiptForSubscription";
import { MiscCollection } from "../screen/Receipt/MiscCollection";
import TripRoaster from '../screen/logistics/TripRoaster/TripRoaster';
import StockReportScreen from '../screen/reports/StockReportScreen';
import DayReportScreen from '../screen/reports/DayReport';
import DayRateScreen from '../screen/reports/DayRate';
import LedgerScreen from '../screen/reports/Ledger';
import BillReportPage from '../screen/reports/BillReportPage';
import ItemReportPage from '../screen/reports/ItemReportPage';
import CreditBalanceReportPage from '../screen/reports/CreditBalanceReportPage';
import CreditLedgerReportPage from '../screen/reports/CreditLedgerReportPage';
import BankTransferScreen from '../screen/finance/BankTransferScreen';
import HOTransferScreen from '../screen/finance/HOTransferScreen';
import IBBranchTransferScreen from '../screen/finance/IBBranchTransferScreen';
import PaymentScreen from '../screen/finance/PaymentScreen';
import BillingPaymentScreen from '../screen/billing/BillingPaymentScreen';
import ViewBillScreen from '../screen/billing/ViewBillScreen';
import StockTake from '../screen/stock/StockTake';
import WastageScreen from '../screen/stock/WastageScreen';
import TransferOutScreen from '../screen/stock/TransferOutScreen';



// Import other screens as needed

const Stack = createNativeStackNavigator();

const MainNavbar = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Billing" component={BillingScreen} />
      <Stack.Screen name="BillingPayment" component={BillingPaymentScreen} />
      <Stack.Screen name="ViewBillScreen" component={ViewBillScreen} />


      <Stack.Screen name="Receiving" component={ReceivingScreen} />
      <Stack.Screen name="Stock Take" component={StockTake} />
      <Stack.Screen name="Wastage" component={WastageScreen} />
      <Stack.Screen name="Transfer Out" component={TransferOutScreen} />

      <Stack.Screen name="Trip" component={TripScreen} />
      <Stack.Screen name="Trip Plan" component={TripPlan} />
      <Stack.Screen name="Trip Roaster" component={TripRoaster} />

      <Stack.Screen name="Trip Expenses" component={TripExpenses} />
      <Stack.Screen name="Delivery" component={TripDelivery} />
      <Stack.Screen name="Receipt For Subscription" component={ReceiptForSubscription} />
      <Stack.Screen name="Misc Collection" component={MiscCollection} />



      <Stack.Screen name="Bank Transfer" component={BankTransferScreen} />
      <Stack.Screen name="HO Transfer" component={HOTransferScreen} />
      <Stack.Screen name="IB Branch Transfer" component={IBBranchTransferScreen} />
      <Stack.Screen name="Payments" component={PaymentScreen} />

      <Stack.Screen name="Day Report" component={DayReportScreen} />
      <Stack.Screen name="Day Rate" component={DayRateScreen} />
      <Stack.Screen name="Ledger" component={LedgerScreen} />
      <Stack.Screen name="Stock Report" component={StockReportScreen} />
      <Stack.Screen name="BillReportPage" component={BillReportPage} />
      <Stack.Screen name="ItemReportPage" component={ItemReportPage} />
      <Stack.Screen name="CreditBalanceReportPage" component={CreditBalanceReportPage} />
      <Stack.Screen name="CreditLedgerReportPage" component={CreditLedgerReportPage} />

      {/* Add other screens here as needed */}
    </Stack.Navigator>
  );
};

export default MainNavbar;