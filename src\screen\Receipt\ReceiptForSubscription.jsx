import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    ActivityIndicator
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Navbar from '../../components/Navbar';
import ScrollOptionsBar from '../../components/ScrollOptionsBar';
import ReceiptModals from './ReusableModal';

/**
 * ReceiptForSubscription Component
 * 
 * A comprehensive interface for managing subscription receipts.
 * Features include receipt details management, item addition, and tabular display.
 */
export const ReceiptForSubscription = () => {
    // Navigation and form states
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Receipt header information
    const [docNumber, setDocNumber] = useState('');
    const [businessDate, setBusinessDate] = useState('18-05-2025');
    const [mobile, setMobile] = useState('8839815443');
    const [customerName, setCustomerName] = useState('');
    const [remarks, setRemarks] = useState('');

    // Item details
    const [itemName, setItemName] = useState('');
    const [itemID, setItemID] = useState(''); // Store selected item ID
    const [qty, setQty] = useState('');
    const [price, setPrice] = useState('');
    const [amount, setAmount] = useState('');
    const [finalAmount, setFinalAmount] = useState('');
    const [pointsPerQty, setPointsPerQty] = useState('');
    const [totalPoints, setTotalPoints] = useState('');

    // Payment details
    const [receiptMode, setReceiptMode] = useState('');
    const [chequeNumber, setChequeNumber] = useState('');
    const [bankName, setBankName] = useState('');
    const [chequeDate, setChequeDate] = useState('02-06-2025');

    // Data table states
    const [items, setItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [isSelectAllItems, setIsSelectAllItems] = useState(false);

    // Summary fields
    const [totalNos, setTotalNos] = useState('');
    const [totalAmount, setTotalAmount] = useState('');
    const [tax, setTax] = useState('');
    const [grandTotal, setGrandTotal] = useState('');

    // Modal states for selection interfaces
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [isLoadingItems, setIsLoadingItems] = useState(false); // Loading state for API calls

    // Branch and authentication
    const [selectedBranch, setSelectedBranch] = useState('');
    const [authToken, setAuthToken] = useState('');

    // Calendar modal state
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [selectedDateField, setSelectedDateField] = useState('');

    // Customer modal state
    const [customerModalVisible, setCustomerModalVisible] = useState(false);
    const [customerSearchTerm, setCustomerSearchTerm] = useState('');
    const [filteredCustomers, setFilteredCustomers] = useState([]);

    // DocName modal state
    const [docNameModalVisible, setDocNameModalVisible] = useState(false);
    const [docNameSearchTerm, setDocNameSearchTerm] = useState('');
    const [filteredDocNames, setFilteredDocNames] = useState([]);
    const [docNameData, setDocNameData] = useState([]);
    const [isLoadingDocNames, setIsLoadingDocNames] = useState(false);

    const [customerData, setCustomerData] = useState([]);
const [selectedCustomer, setSelectedCustomer] = useState(null);
const [custID, setCustID] = useState('');
    // Dropdown options
    const receiptModeOptions = ['Cash', 'Card', 'Cheque', 'Online Transfer', 'UPI'];
    const itemOptions = []; // Will be populated from API

    // Customer data
    // const customerData = [
    //     'John Doe',
    //     'Jane Smith',
    //     'Mike Johnson',
    //     'Sarah Williams',
    //     'David Brown',
    //     'Lisa Davis',
    //     'Robert Wilson',
    //     'Emma Thompson',
    //     'James Taylor',
    //     'Mary Anderson'
    // ];

    // Load authentication data and branch info on component mount
    useEffect(() => {
        loadAuthData();
    }, []);


    /**
 * Fetch customers from API based on customer name
 */
const fetchCustomersByName = async (customerName) => {
    if (!selectedBranch || !authToken || !customerName || customerName.length < 2) {
        return [];
    }

    try {
        const url = `https://retailuat.abisaio.com:9001/api/POSCustomerSerch/Get/${selectedBranch}/RT/CUSTNAME/${customerName}`;
        console.log('Fetching customers from:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Customer API Response:', data);
        
        return data.customers || [];
    } catch (error) {
        console.error('Error fetching customers:', error);
        Alert.alert('Error', 'Failed to fetch customers from server');
        return [];
    }
};


/**
 * Fetch customer from API based on mobile number
 */
const fetchCustomerByMobile = async (mobileNumber) => {
    if (!authToken || !mobileNumber || mobileNumber.length !== 10) {
        return null;
    }

    setIsLoading(true);
    try {
        const url = `https://retailuat.abisaio.com:9001/api/POSCustomer/MOBILE/${mobileNumber}`;
        console.log('Fetching customer by mobile from:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Customer by Mobile API Response:', data);
        
        if (data.customers && data.customers.length > 0) {
            // If multiple customers found, show modal to select
            if (data.customers.length > 1) {
                setCustomerData(data.customers);
                setFilteredCustomers(data.customers);
                setCustomerModalVisible(true);
                return data.customers;
            } else {
                // If only one customer, auto-select it
                const customer = data.customers[0];
                setCustomerName(customer.customerName);
                setCustID(customer.custID);
                setSelectedCustomer(customer);
                return customer;
            }
        } else {
            // No customers found
            Alert.alert('Info', 'No customer found with this mobile number');
            return null;
        }
    } catch (error) {
        console.error('Error fetching customer by mobile:', error);
        Alert.alert('Error', 'Failed to fetch customer details');
        return null;
    } finally {
        setIsLoading(false);
    }
};

    /**
     * Load authentication token and branch data from AsyncStorage
     */
    const loadAuthData = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const branch = await AsyncStorage.getItem('selectedBranch');

            if (token) {
                setAuthToken(token);
            }

            if (branch) {
                const branchData = JSON.parse(branch);
                setSelectedBranch(branchData.BranchId);
                console.log('----Selected Branch --', branchData.BranchId);
            }
        } catch (error) {
            console.error('Error loading auth data:', error);
            Alert.alert('Error', 'Failed to load authentication data');
        }
    };

    /**
     * Fetch items from API based on branch ID
     */
    const fetchItemsFromAPI = async () => {
        if (!selectedBranch || !authToken) {
            Alert.alert('Error', 'Missing authentication data or branch information');
            return [];
        }

        setIsLoadingItems(true);
        try {
            const url = `https://retailUAT.abisaio.com:9001/api/ItemByParm/GetbyCategory/L102/SR001`;
            console.log('-----', url);

            const response = await fetch(url,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data || [];
        } catch (error) {
            console.error('Error fetching items:', error);
            Alert.alert('Error', 'Failed to fetch items from server');
            return [];
        } finally {
            setIsLoadingItems(false);
        }
    };

    /**
     * Fetch DocNames from API
     */
    const fetchDocNamesFromAPI = async () => {
        if (!selectedBranch) {
            Alert.alert('Error', 'Missing branch information');
            return [];
        }

        setIsLoadingDocNames(true);
        try {
            const url = `https://retailuat.abisibg.com/api/v1/fetchsale?BranchId=${selectedBranch}&stage=PL&FromDate=20250101&ToDate=20250616`;
            console.log('Fetching DocNames from:', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Add console logs to see the response
            console.log('DocNames API Response:', data);
            console.log('Response length:', data?.length);

            // Extract DocName from the response
            const docNames = data.map(item => ({
                DocID: item.DocID,
                DocName: item.DocName
            }));

            console.log('Processed DocNames:', docNames);

            setDocNameData(docNames);
            setFilteredDocNames(docNames);
            return docNames;
        } catch (error) {
            console.error('Error fetching DocNames:', error);
            Alert.alert('Error', 'Failed to fetch DocNames from server');
            return [];
        } finally {
            setIsLoadingDocNames(false);
        }
    };

    // Filter modal data when search term changes
    useEffect(() => {
        if (searchTerm) {
            let filtered;
            if (modalType === 'itemName') {
                // For items, search in itemName property
                filtered = modalData.filter(item =>
                    item.itemName.toLowerCase().includes(searchTerm.toLowerCase())
                );
            } else {
                // For other dropdowns (like receiptMode), search directly in string array
                filtered = modalData.filter(item =>
                    (typeof item === 'string' ? item : item.itemName).toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData, modalType]);

    // Filter customer data when search term changes
    useEffect(() => {
        if (customerSearchTerm) {
            const filtered = customerData.filter(customer =>
                customer.customerName.toLowerCase().includes(customerSearchTerm.toLowerCase())
            );
            setFilteredCustomers(filtered);
        } else {
            setFilteredCustomers(customerData);
        }
    }, [customerSearchTerm]);

    // Filter DocName data when search term changes
    useEffect(() => {
        if (docNameSearchTerm) {
            const filtered = docNameData.filter(item =>
                item.DocName.toLowerCase().includes(docNameSearchTerm.toLowerCase())
            );
            setFilteredDocNames(filtered);
        } else {
            setFilteredDocNames(docNameData);
        }
    }, [docNameSearchTerm, docNameData]);

    // Handle select all checkbox for items
    useEffect(() => {
        if (isSelectAllItems) {
            const allIndices = items.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllItems]);

    // Calculate amount when qty and price change
    useEffect(() => {
        if (qty && price) {
            const calculatedAmount = (parseFloat(qty) * parseFloat(price)).toFixed(2);
            setAmount(calculatedAmount);
            setFinalAmount(calculatedAmount);
        }
    }, [qty, price]);

    /**
     * Handles actions from the ScrollOptionsBar
     */
    const handleOptionPress = async (option) => {
        setSelectedScrollOption(option);
        setIsLoading(true); // Start loading

        try {
            switch (option) {
                case 'New':
                    resetForm();
                    break;
                case 'Save':
                    await saveReceipt();
                    break;
                case 'View':
                    // View logic would be implemented here
                    break;
                case 'Cancel':
                    resetForm();
                    break;
                case 'Post':
                    await postReceipt();
                    break;
                default:
                    break;
            }
        } finally {
            setIsLoading(false); // Stop loading
        }
    };


    /**
     * Resets all form fields to their initial state
     */
    const resetForm = () => {
        setDocNumber('');
        setBusinessDate('');
        setMobile('');
        setCustomerName('');
        setCustID(''); // Add this
        setSelectedCustomer(null); // Add this
        setRemarks('');
        setItemName('');
        setItemID('');
        setQty('');
        setPrice('');
        setAmount('');
        setFinalAmount('');
        setPointsPerQty('');
        setTotalPoints('');
        setReceiptMode('');
        setChequeNumber('');
        setBankName('');
        setChequeDate('02-06-2025');
        setItems([]);
        setSelectedRows([]);
        setIsSelectAllItems(false);
        setTotalNos('');
        setTotalAmount('');
        setTax('');
        setGrandTotal('');
        setCustomerData([]); // Add this
        setFilteredCustomers([]); // Add this
    };

    /**
     * Validates and saves the receipt
     */
    const saveReceipt = async () => {
        if (!customerName) {
            Alert.alert('Validation Error', 'Please enter customer name');
            return;
        }
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        Alert.alert('Success', 'Receipt saved successfully');
    };
    /**
     * Posts the receipt
     */
    const postReceipt = async () => {
        if (!customerName) {
            Alert.alert('Validation Error', 'Please enter customer name');
            return;
        }
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        Alert.alert('Success', 'Receipt posted successfully');
    };

    /**
     * Toggles selection of a table row
     */
    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllItems(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            if (selectedRows.length + 1 === items.length) {
                setIsSelectAllItems(true);
            }
        }
    };

    /**
     * Removes selected rows from the items table
     */
    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }
        const newItems = items.filter((_, index) => !selectedRows.includes(index));
        setItems(newItems);
        setSelectedRows([]);
        setIsSelectAllItems(false);
        calculateTotals(newItems);
    };

    /**
     * Validates and adds a new item to the table
     */
    const handleAddItem = async () => {
        if (!itemName || !qty || !price) {
            Alert.alert('Validation Error', 'Please fill all item details');
            return;
        }

        setIsLoading(true); // Start loading

        try {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            const newItem = {
                set: items.length + 1,
                itemID: itemID,
                itemName: itemName,
                return: '',
                kas: qty,
                batchNumber: '',
                rate: price,
                discount: '0',
                disc: finalAmount
            };

            const updatedItems = [...items, newItem];
            setItems(updatedItems);
            calculateTotals(updatedItems);

            // Reset item fields
            setItemName('');
            setItemID('');
            setQty('');
            setPrice('');
            setAmount('');
            setFinalAmount('');
            setPointsPerQty('');
            setTotalPoints('');
        } finally {
            setIsLoading(false); // Stop loading
        }
    };

    /**
     * Calculate totals based on items
     */
    const calculateTotals = (itemsList) => {
        const totalQty = itemsList.reduce((sum, item) => sum + parseFloat(item.kas || 0), 0);
        const totalAmt = itemsList.reduce((sum, item) => sum + parseFloat(item.disc || 0), 0);
        const taxAmount = totalAmt * 0.18; // Assuming 18% tax
        const grandTotalAmt = totalAmt + taxAmount;

        setTotalNos(totalQty.toString());
        setTotalAmount(totalAmt.toFixed(2));
        setTax(taxAmount.toFixed(2));
        setGrandTotal(grandTotalAmt.toFixed(2));
    };

    /**
     * Opens selection modal with appropriate data
     */
    const openModal = async (type) => {
        setIsLoading(true); // Start loading
        setSearchTerm('');

        try {
            let dataToShow = [];
            switch (type) {
                case 'receiptMode':
                    dataToShow = receiptModeOptions;
                    break;
                case 'itemName':
                    dataToShow = await fetchItemsFromAPI();
                    break;
                default:
                    dataToShow = [];
            }

            setModalData(dataToShow);
            setFilteredModalData(dataToShow);
            setModalType(type);
            setModalVisible(true);
        } finally {
            setIsLoading(false); // Stop loading
        }
    };


    /**
     * Handles item selection from modal
     */
    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'receiptMode':
                setReceiptMode(item);
                break;
            case 'itemName':
                // item is an object with itemID and itemName
                setItemName(item.itemName);
                setItemID(item.itemID);
                break;
            default:
                break;
        }
        setModalVisible(false);
    };

    /**
     * Opens calendar modal for date selection
     */
    const openCalendar = async (field) => {
        setIsLoading(true); // Start loading

        try {
            // Simulate loading delay
            await new Promise(resolve => setTimeout(resolve, 500));
            setSelectedDateField(field);
            setCalendarVisible(true);
        } finally {
            setIsLoading(false); // Stop loading
        }
    };

    /**
     * Handles date selection from calendar
     */
    const handleDateSelect = (day) => {
        const formattedDate = day.dateString.split('-').reverse().join('-'); // Convert YYYY-MM-DD to DD-MM-YYYY
        if (selectedDateField === 'businessDate') {
            setBusinessDate(formattedDate);
        } else if (selectedDateField === 'chequeDate') {
            setChequeDate(formattedDate);
        }
        setCalendarVisible(false);
    };

    /**
     * Opens customer selection modal
     */
/**
 * Opens customer selection modal
 */
const openCustomerModal = async () => {
    setIsLoading(true); // Start loading

    try {
        // Simulate loading delay
        await new Promise(resolve => setTimeout(resolve, 300));
        setCustomerSearchTerm('');
        setFilteredCustomers([]);
        setCustomerData([]);
        setCustomerModalVisible(true);
    } finally {
        setIsLoading(false); // Stop loading
    }
};

    /**
     * Handles customer selection
     */
    const handleCustomerSelect = (customer) => {
        setCustomerName(customer.customerName);
        setMobile(customer.mobile);
        setCustID(customer.custID);
        setSelectedCustomer(customer);
        setCustomerModalVisible(false);
    };

    /**
     * Handles customer search
     */
    const handleCustomerSearch = async () => {
        if (customerSearchTerm && customerSearchTerm.length >= 2) {
            const customers = await fetchCustomersByName(customerSearchTerm);
            setFilteredCustomers(customers);
            setCustomerData(customers);
        } else {
            setFilteredCustomers([]);
            setCustomerData([]);
        }
    };



/**
 * Handle mobile number change and auto-fetch customer if 10 digits
 */
const handleMobileChange = (value) => {
    setMobile(value);
    
    // Auto-fetch customer when mobile number is 10 digits
    if (value.length === 10) {
        fetchCustomerByMobile(value);
    } else if (value.length < 10) {
        // Clear customer data if mobile is less than 10 digits
        setCustomerName('');
        setCustID('');
        setSelectedCustomer(null);
        setCustomerData([]);
        setFilteredCustomers([]);
    }
};



    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (customerSearchTerm && customerSearchTerm.length >= 2) {
                handleCustomerSearch();
            } else {
                setFilteredCustomers([]);
            }
        }, 500); // 500ms debounce
    
        return () => clearTimeout(timeoutId);
    }, [customerSearchTerm]);

    /**
     * Opens DocName selection modal
     */
    const openDocNameModal = async () => {
        setIsLoading(true); // Start loading

        try {
            setDocNameSearchTerm('');
            setDocNameData([]);
            setFilteredDocNames([]);
            setDocNameModalVisible(true);
            // Fetch data after modal opens
            await fetchDocNamesFromAPI();
        } finally {
            setIsLoading(false); // Stop loading
        }
    };

    /**
     * Handles DocName selection
     */
    const handleDocNameSelect = (docItem) => {
        console.log('Selected DocName:', docItem);
        setDocNumber(docItem.DocName); // Changed from DocID to DocName
        setDocNameModalVisible(false);
    };

    /**
     * Get current date in YYYY-MM-DD format for calendar
     */
    const getCurrentDate = () => {
        const today = new Date();
        return today.toISOString().split('T')[0];
    };

    /**
     * Convert DD-MM-YYYY to YYYY-MM-DD for calendar
     */
    const convertDateForCalendar = (dateString) => {
        if (!dateString) return getCurrentDate();
        const parts = dateString.split('-');
        if (parts.length === 3) {
            return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        return getCurrentDate();
    };

    return (
        <View style={styles.container}>
            <Navbar />

            <ScrollOptionsBar
                title="Receipt for Subscription"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    {/* Header Section */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Doc#</Text>
                            <View style={styles.customerInputContainer}>
                                <TextInput
                                    style={styles.customerInput}
                                    placeholder="Doc Number"
                                    placeholderTextColor="#888"
                                    value={docNumber}
                                    onChangeText={setDocNumber}
                                />
                                <TouchableOpacity
                                    style={styles.searchCustomerButton}
                                    onPress={openDocNameModal} // Remove async from here
                                >
                                    <Icon name="search" size={20} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>

                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Business Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder="Business Date"
                                    placeholderTextColor="#888"
                                    value={businessDate}
                                    onChangeText={setBusinessDate}
                                    editable={false}
                                />
                                <TouchableOpacity
                                    style={styles.calendarButton}
                                    onPress={() => openCalendar('businessDate')} // Remove async from here
                                >
                                    <Icon name="date-range" size={20} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                    <View style={styles.inputContainer}>
    <Text style={styles.labelText}>Mobile</Text>
    <TextInput
        style={styles.input}
        placeholder="Mobile Number"
        placeholderTextColor="#888"
        value={mobile}
        onChangeText={handleMobileChange} // Changed from setMobile to handleMobileChange
        keyboardType="numeric"
        maxLength={10}
    />
</View>

                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Customer Name</Text>
                            <View style={styles.customerInputContainer}>
                                <Icon name="person" size={20} color="#888" style={styles.customerIcon} />
                                <TextInput
                                    style={styles.customerInput}
                                    placeholder="Customer Name"
                                    placeholderTextColor="#888"
                                    value={customerName}
                                    onChangeText={setCustomerName}
                                />
                                <TouchableOpacity
                                    style={styles.searchCustomerButton}
                                    onPress={openCustomerModal} // Remove async from here
                                >
                                    <Icon name="search" size={20} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    <View style={styles.fullWidthRow}>
                        <Text style={styles.labelText}>Remarks</Text>
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Add Remarks"
                            placeholderTextColor="#888"
                            value={remarks}
                            onChangeText={setRemarks}
                            multiline={true}
                            numberOfLines={2}
                        />
                    </View>

                    {/* Item Details Section */}
                    <View style={styles.sectionHeaderContainer}>
                        <Text style={styles.sectionTitle}>Item Details</Text>
                    </View>

                    {/* First Row of Item Details */}
                    <View style={styles.itemRow}>
                        <View style={styles.dropdownContainer}>
                            <Text style={styles.labelText}>Item Name</Text>
                            <TouchableOpacity
                                style={styles.dropdown}
                                onPress={() => openModal('itemName')} // Remove async from here
                            >
                                <Text style={[styles.inputText, !itemName && styles.placeholderText]}>
                                    {itemName || 'Select Item'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>Qty</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Qty"
                                placeholderTextColor="#888"
                                value={qty}
                                onChangeText={setQty}
                                keyboardType="numeric"
                            />
                        </View>

                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>Price</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Price"
                                placeholderTextColor="#888"
                                value={price}
                                onChangeText={setPrice}
                                keyboardType="numeric"
                            />
                        </View>

                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>Amount</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Amount"
                                placeholderTextColor="#888"
                                value={amount}
                                editable={false}
                            />
                        </View>

                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>Final Amount</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Final Amount"
                                placeholderTextColor="#888"
                                value={finalAmount}
                                onChangeText={setFinalAmount}
                                keyboardType="numeric"
                            />
                        </View>
                    </View>

                    {/* Second Row: Points, Receipt Mode and Add Button */}
                    <View style={styles.receiptModeAddRow}>
                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>Points / Qty</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Points/Qty"
                                placeholderTextColor="#888"
                                value={pointsPerQty}
                                onChangeText={setPointsPerQty}
                                keyboardType="numeric"
                            />
                        </View>

                        <View style={styles.smallInputContainer}>
                            <Text style={styles.labelText}>TOTAL points</Text>
                            <TextInput
                                style={styles.smallInput}
                                placeholder="Total Points"
                                placeholderTextColor="#888"
                                value={totalPoints}
                                onChangeText={setTotalPoints}
                                keyboardType="numeric"
                            />
                        </View>

                        <View style={styles.receiptModeContainer}>
                            <Text style={styles.labelText}>Receipt Mode</Text>
                            <TouchableOpacity
                                style={styles.receiptModeDropdown}
                                onPress={() => openModal('receiptMode')} // Remove async from here
                            >
                                <Text style={[styles.inputText, !receiptMode && styles.placeholderText]}>
                                    {receiptMode || 'Select Receipt Mode'}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>

                        <TouchableOpacity style={styles.addButton} onPress={handleAddItem}>
                            <Text style={styles.addButtonText}>Add +</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Payment Details Section */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft No/Ref No</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Cheque/Draft No/Ref No"
                                placeholderTextColor="#888"
                                value={chequeNumber}
                                onChangeText={setChequeNumber}
                            />
                        </View>

                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Bank Name</Text>
                            <TextInput
                                style={styles.input}
                                placeholder="Bank Name"
                                placeholderTextColor="#888"
                                value={bankName}
                                onChangeText={setBankName}
                            />
                        </View>

                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder="Cheque/Draft Date"
                                    placeholderTextColor="#888"
                                    value={chequeDate}
                                    onChangeText={setChequeDate}
                                    editable={false}
                                />
                                <TouchableOpacity
                                    style={styles.calendarButton}
                                    onPress={() => openCalendar('chequeDate')} // Remove async from here
                                >
                                    <Icon name="date-range" size={20} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Items Table */}
                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <View style={styles.checkboxCell}>
                                <CheckBox
                                    value={isSelectAllItems}
                                    onValueChange={setIsSelectAllItems}
                                    tintColors={{ true: '#FDC500', false: '#FDC500' }}
                                />
                            </View>
                            <View style={styles.setCell}><Text style={styles.tableHeaderText}>#</Text></View>
                            <View style={styles.setCell}><Text style={styles.tableHeaderText}>Set</Text></View>
                            <View style={styles.itemCell}><Text style={styles.tableHeaderText}>Item</Text></View>
                            <View style={styles.returnCell}><Text style={styles.tableHeaderText}>Return</Text></View>
                            <View style={styles.kasCell}><Text style={styles.tableHeaderText}>Kas</Text></View>
                            <View style={styles.batchCell}><Text style={styles.tableHeaderText}>BatchNumber</Text></View>
                            <View style={styles.rateCell}><Text style={styles.tableHeaderText}>Rate</Text></View>
                            <View style={styles.discountCell}><Text style={styles.tableHeaderText}>Discount</Text></View>
                            <View style={styles.discCell}><Text style={styles.tableHeaderText}>Disc</Text></View>
                        </View>

                        {items.length > 0 ? (
                            items.map((item, index) => (
                                <View key={index} style={styles.tableRow}>
                                    <View style={styles.checkboxCell}>
                                        <CheckBox
                                            value={selectedRows.includes(index)}
                                            onValueChange={() => handleRowSelection(index)}
                                            tintColors={{ true: '#02096A', false: '#999' }}
                                        />
                                    </View>
                                    <View style={styles.setCell}><Text style={styles.tableCell}>{index + 1}</Text></View>
                                    <View style={styles.setCell}><Text style={styles.tableCell}>{item.set}</Text></View>
                                    <View style={styles.itemCell}><Text style={styles.tableCell}>{item.itemName}</Text></View>
                                    <View style={styles.returnCell}><Text style={styles.tableCell}>{item.return}</Text></View>
                                    <View style={styles.kasCell}><Text style={styles.tableCell}>{item.kas}</Text></View>
                                    <View style={styles.batchCell}><Text style={styles.tableCell}>{item.batchNumber}</Text></View>
                                    <View style={styles.rateCell}><Text style={styles.tableCell}>{item.rate}</Text></View>
                                    <View style={styles.discountCell}><Text style={styles.tableCell}>{item.discount}</Text></View>
                                    <View style={styles.discCell}><Text style={styles.tableCell}>{item.disc}</Text></View>
                                </View>
                            ))
                        ) : (
                            <View style={styles.emptyTableRow}>
                                <Text style={styles.emptyTableText}>No Data</Text>
                            </View>
                        )}
                    </View>

                    {/* Summary Section */}
                    <View style={styles.summaryRow}>
                        <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                            <Text style={styles.deleteButtonText}>Delete selected</Text>
                        </TouchableOpacity>

                        <View style={styles.summaryInputContainer}>
                            <Text style={styles.labelText}>Nos</Text>
                            <TextInput
                                style={styles.summaryInput}
                                placeholder="Nos"
                                placeholderTextColor="#888"
                                value={totalNos}
                                editable={false}
                            />
                        </View>

                        <View style={styles.summaryInputContainer}>
                            <Text style={styles.labelText}>Amount</Text>
                            <TextInput
                                style={styles.summaryInput}
                                placeholder="Amount"
                                placeholderTextColor="#888"
                                value={totalAmount}
                                editable={false}
                            />
                        </View>

                        <View style={styles.summaryInputContainer}>
                            <Text style={styles.labelText}>Tax</Text>
                            <TextInput
                                style={styles.summaryInput}
                                placeholder="Tax"
                                placeholderTextColor="#888"
                                value={tax}
                                editable={false}
                            />
                        </View>

                        <View style={styles.summaryInputContainer}>
                            <Text style={styles.labelText}>Total</Text>
                            <TextInput
                                style={styles.summaryInput}
                                placeholder="Total"
                                placeholderTextColor="#888"
                                value={grandTotal}
                                editable={false}
                            />
                        </View>
                    </View>
                </View>
            </ScrollView>

            {/* Modal Component */}
            <ReceiptModals
                // Selection Modal Props
                modalVisible={modalVisible}
                setModalVisible={setModalVisible}
                modalType={modalType}
                modalData={modalData}
                filteredModalData={filteredModalData}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                isLoadingItems={isLoadingItems}
                handleModalItemSelect={handleModalItemSelect}

                // Customer Modal Props
                customerModalVisible={customerModalVisible}
                setCustomerModalVisible={setCustomerModalVisible}
                customerSearchTerm={customerSearchTerm}
                setCustomerSearchTerm={setCustomerSearchTerm}
                filteredCustomers={filteredCustomers}
                handleCustomerSelect={handleCustomerSelect}
                handleCustomerSearch={handleCustomerSearch}
                isMobileSearch={filteredCustomers.length > 0 && mobile.length === 10}

                // Calendar Modal Props
                calendarVisible={calendarVisible}
                setCalendarVisible={setCalendarVisible}
                handleDateSelect={handleDateSelect}
                convertDateForCalendar={convertDateForCalendar}
                selectedDateField={selectedDateField}
                businessDate={businessDate}
                chequeDate={chequeDate}

                // DocName Modal Props
                docNameModalVisible={docNameModalVisible}
                setDocNameModalVisible={setDocNameModalVisible}
                docNameSearchTerm={docNameSearchTerm}
                setDocNameSearchTerm={setDocNameSearchTerm}
                filteredDocNames={filteredDocNames}
                isLoadingDocNames={isLoadingDocNames}
                handleDocNameSelect={handleDocNameSelect}
            />
            {isLoading && (
                <View style={styles.loaderOverlay}>
                    <View style={styles.loaderContainer}>
                        <ActivityIndicator size="large" color="#FDC500" />
                        <Text style={styles.loaderText}>Loading...</Text>
                    </View>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    contentContainer: {
        flex: 1,
        padding: 10,
    },
    sectionContainer: {
        backgroundColor: '#EBEBEB',
        borderRadius: 10,
        padding: 15,
        marginHorizontal: 8,
        marginVertical: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    sectionHeaderContainer: {
        backgroundColor: '#D0D0D0',
        padding: 10,
        marginVertical: 8,
        borderRadius: 5,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        textAlign: 'center',
        fontFamily: 'Poppins',
        letterSpacing: 0.5,
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginBottom: 15,
        gap: 10,
    },
    fullWidthRow: {
        marginBottom: 15,
    },
    itemRow: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 15,
    },
    receiptModeAddRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginBottom: 15,
        gap: 15,
    },
    receiptModeContainer: {
        flex: 1,
        minWidth: 200,
    },
    receiptModeDropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 45,
    },
    inputContainer: {
        flex: 1,
        minWidth: 150,
    },
    labelText: {
        fontSize: 12,
        color: '#333',
        marginBottom: 5,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },

    // Add these to your existing styles object
    loaderOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
    },
    loaderContainer: {
        backgroundColor: 'white',
        padding: 30,
        borderRadius: 15,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    loaderText: {
        marginTop: 15,
        fontSize: 16,
        color: '#333',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    input: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 45,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
    },
    remarksInput: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        minHeight: 60,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        textAlignVertical: 'top',
        fontWeight: 'bold',
    },
    dateInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    dateInput: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 45,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        marginRight: 5,
    },
    calendarButton: {
        backgroundColor: '#02096A',
        borderRadius: 8,
        padding: 12,
        height: 45,
        width: 45,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    customerInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingLeft: 12,
    },
    customerIcon: {
        marginRight: 8,
    },
    customerInput: {
        flex: 1,
        padding: 12,
        height: 45,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
    },
    searchCustomerButton: {
        backgroundColor: '#02096A',
        borderRadius: 8,
        padding: 12,
        height: 45,
        width: 45,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        marginLeft: 5,
        marginRight: 5,
    },
    dropdownContainer: {
        flex: 1,
        minWidth: 150,
    },
    dropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 45,
    },
    inputText: {
        color: '#333',
        fontFamily: 'Poppins',
        fontSize: 14,
        fontWeight: 'bold',
    },
    placeholderText: {
        color: '#888',
        fontWeight: 'normal',
    },
    dropdownIcon: {
        color: '#333',
        fontSize: 16,
        fontWeight: 'bold',
    },
    smallInputContainer: {
        minWidth: 80,
        maxWidth: 120,
    },
    smallInput: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 8,
        height: 40,
        fontFamily: 'Poppins',
        fontSize: 12,
        color: '#333',
        textAlign: 'center',
        fontWeight: 'bold',
    },
    addButton: {
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 45,
        minWidth: 80,
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    tableContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        borderWidth: 1,
        borderColor: '#ccc',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#041C44',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableHeaderText: {
        fontWeight: 'bold',
        fontSize: 12,
        color: 'white',
        fontFamily: 'Poppins',
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableCell: {
        fontSize: 12,
        color: '#333',
        fontFamily: 'Poppins',
        textAlign: 'center',
        fontWeight: 'bold',
    },
    checkboxCell: {
        width: 30,
        justifyContent: 'center',
        alignItems: 'center',
    },
    setCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    itemCell: {
        flex: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
    returnCell: {
        width: 60,
        justifyContent: 'center',
        alignItems: 'center',
    },
    kasCell: {
        width: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },
    batchCell: {
        width: 80,
        justifyContent: 'center',
        alignItems: 'center',
    },
    rateCell: {
        width: 60,
        justifyContent: 'center',
        alignItems: 'center',
    },
    discountCell: {
        width: 70,
        justifyContent: 'center',
        alignItems: 'center',
    },
    discCell: {
        width: 60,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableRow: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
        fontStyle: 'italic',
    },
    summaryRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginTop: 15,
        gap: 10,
    },
    summaryInputContainer: {
        minWidth: 80,
        maxWidth: 120,
    },
    summaryInput: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 8,
        height: 40,
        fontFamily: 'Poppins',
        fontSize: 12,
        color: '#333',
        textAlign: 'center',
        fontWeight: 'bold',
    },
});

export default ReceiptForSubscription;