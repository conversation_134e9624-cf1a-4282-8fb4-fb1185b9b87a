import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';
import CustomLoader from '../../../components/CustomLoader';
import { local_signage } from '../../../../const';

export const TripRoaster = () => {
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [vehicleNo, setVehicleNo] = useState('');
    const [startReading, setStartReading] = useState('');
    const [endReading, setEndReading] = useState('');
    const [remarks, setRemarks] = useState('');
    const [selectedOperator, setSelectedOperator] = useState('');
    const [selectedOperatorId, setSelectedOperatorId] = useState('');
    const [selectedRows, setSelectedRows] = useState([]);
    const [operatorsList, setOperatorsList] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [isSelectAllOperators, setIsSelectAllOperators] = useState(false);
    const [authToken, setAuthToken] = useState('');
    const [currentBranchId, setCurrentBranchId] = useState('');
    const [currentBranch, setCurrentBranch] = useState(null);
    const [loading, setLoading] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);

    // API Data states
    const [vehicleOptions, setVehicleOptions] = useState([]);
    const [operatorOptions, setOperatorOptions] = useState([]);
    const [branchOptions, setBranchOptions] = useState([]);

    // Get branch name for display in header
    const getBranchName = () => {
        if (currentBranch) {
            return currentBranch.branchName || 'Selected Branch';
        }
        return 'Branch Not Selected';
    };

    useEffect(() => {
        const initializeApp = async () => {
            await getAuthToken();
        };
        initializeApp();
    }, []);

    useEffect(() => {
        // Load branches after auth token is available
        if (authToken) {
            loadBranches();
        }
    }, [authToken]);

    useEffect(() => {
        if (authToken && currentBranchId) {
            console.log('🚀 Prerequisites met - loading initial data...');
            loadVehicleNumbers(currentBranchId);
            loadOperators();
        }
    }, [authToken, currentBranchId]);

    useEffect(() => {
        if (searchTerm) {
            const filtered = modalData.filter(item => {
                if (typeof item === 'string') {
                    return item.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.VehicleId) {
                    return item.VehicleId.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.UserName) {
                    return item.UserName.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.branchName) {
                    return item.branchName.toLowerCase().includes(searchTerm.toLowerCase());
                }
                return false;
            });
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    useEffect(() => {
        if (isSelectAllOperators) {
            const allIndices = operatorsList.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllOperators]);

    useEffect(() => {
        if (modalVisible && modalType === 'vehicleNo' && vehicleOptions.length > 0) {
            setModalData(vehicleOptions);
            setFilteredModalData(vehicleOptions);
        }
    }, [vehicleOptions, modalVisible, modalType]);

    useEffect(() => {
        if (modalVisible && modalType === 'operator' && operatorOptions.length > 0) {
            setModalData(operatorOptions);
            setFilteredModalData(operatorOptions);
        }
    }, [operatorOptions, modalVisible, modalType]);

    useEffect(() => {
        if (modalVisible && modalType === 'branch' && branchOptions.length > 0) {
            setModalData(branchOptions);
            setFilteredModalData(branchOptions);
        }
    }, [branchOptions, modalVisible, modalType]);

    const getAuthToken = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            if (token) {
                setAuthToken(token);
            } else {
                Alert.alert('Authentication Error', 'Please login again to continue');
            }

            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                setCurrentBranch(branch);

                let branchId = branch.branchID || branch.id || branch.branchId;
                if (branchId) {
                    setCurrentBranchId(branchId);
                }
            }
        } catch (error) {
            console.error('Error getting stored data:', error);
            Alert.alert('Error', 'Failed to retrieve stored data');
        }
    };

    const makeApiCall = async (url, options = {}) => {
        if (!authToken) {
            Alert.alert('Authentication Error', 'Please login again to continue');
            return null;
        }

        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
            });

            if (response.status === 401) {
                Alert.alert('Session Expired', 'Please login again to continue');
                return null;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API call error:', error);
            Alert.alert('Error', `Failed to fetch data: ${error.message}`);
            return null;
        }
    };

    const loadBranches = async () => {
        if (!authToken) return;

        setLoading(true);
        try {
            const url = `https://retailUAT.abisaio.com:9001/api/Company/GetBranchList/ALL/SEGMENT/ALL`;
            console.log('🟡 Branch API URL:', url);
            console.log('🟡 Branch API - Using Auth Token:', authToken);

            const data = await makeApiCall(url);
            if (data) {
                console.log('✅ Branches loaded:', data.length, 'items');
                console.log('✅ Branches data:', data);
                setBranchOptions(data);
                if (modalType === 'branch') {
                    setModalData(data);
                    setFilteredModalData(data);
                }
            } else {
                console.warn('⚠️ No branches data received');
            }
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        } finally {
            setLoading(false);
            console.log('🟡 Branches loading completed');
        }
    };

    const loadVehicleNumbers = async (branchId) => {
        if (!branchId) return;

        setLoading(true);
        try {
            const url = `${local_signage}/api/v1/vehicleno?BranchID=${branchId}`;
            const data = await makeApiCall(url);
            if (data) {
                setVehicleOptions(data);
                if (modalType === 'vehicleNo') {
                    setModalData(data);
                    setFilteredModalData(data);
                }
            }
        } catch (error) {
            console.error('Error loading vehicle numbers:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadOperators = async () => {
        if (!currentBranchId) return;

        setLoading(true);
        try {
            const currentDate = new Date();
            const businessDate = currentDate.getFullYear().toString() +
                                (currentDate.getMonth() + 1).toString().padStart(2, '0') +
                                currentDate.getDate().toString().padStart(2, '0');

            const url = `https://retailuat.abisibg.com/api/v1/rosteremployee?BranchID=L101&BusinessDate=20250319`;
            const data = await makeApiCall(url);
            if (data) {
                setOperatorOptions(data);
                if (modalType === 'operator') {
                    setModalData(data);
                    setFilteredModalData(data);
                }
            }
        } catch (error) {
            console.error('Error loading operators:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            saveTrip();
        } else if (option === 'View') {
            // Logic for view
        } else if (option === 'Cancel') {
            resetForm();
        }
    };

    const resetForm = () => {
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setRemarks('');
        setSelectedOperator('');
        setSelectedOperatorId('');
        setSelectedRows([]);
        setOperatorsList([]);
    };

    const saveTrip = async () => {
        if (operatorsList.length === 0) {
            Alert.alert('Validation Error', 'Please add at least one operator');
            return;
        }

        if (!currentBranchId) {
            Alert.alert('Validation Error', 'Branch information is missing. Please login again.');
            return;
        }

        setLoading(true);

        try {
            const userData = await AsyncStorage.getItem('userData');
            const user = userData ? JSON.parse(userData) : null;
            const currentUserId = user?.userId || user?.id || user?.empId || 'SYSTEM';

            const currentDate = new Date().toISOString();

            const instaTrips = operatorsList.map(operator => ({
                remarks: operator.remarks || "",
                startReading: parseInt(operator.startReading) || 0,
                endReading: parseInt(operator.endReading) || 0,
                operatorEmpId: operator.operatorId,
                vehicleNo: operator.vehicleNo,
                tpVehicleNo: operator.vehicleNo
            }));

            const tripData = {
                branchId: currentBranchId,
                tripDate: currentDate,
                createdUserId: currentUserId,
                instaTrips: instaTrips
            };
            console.log(tripData);
            
            const response = await makeApiCall(`https://retailuat.abisaio.com:9001/api/TripRoster`, {
                method: 'POST', // Changed from PUT to POST
                body: JSON.stringify(tripData),
            });

            if (response) {
                Alert.alert(
                    'Success',
                    'Trip saved successfully!',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                resetForm();
                            }
                        }
                    ]
                );
            } else {
                Alert.alert('Error', 'Failed to save trip. Please try again.');
            }
        } catch (error) {
            console.error('Error saving trip:', error);
            Alert.alert('Error', `Failed to save trip: ${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllOperators(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            if (selectedRows.length + 1 === operatorsList.length) {
                setIsSelectAllOperators(true);
            }
        }
    };

    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newOperators = operatorsList.filter((_, index) => !selectedRows.includes(index));
        setOperatorsList(newOperators);
        setSelectedRows([]);
        setIsSelectAllOperators(false);
    };

    const handleClearTripDetails = () => {
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setRemarks('');
        setSelectedOperator('');
        setSelectedOperatorId('');
    };

    const openModal = async (type) => {
        setSearchTerm('');
        setModalType(type);
        setModalVisible(true);

        switch (type) {
            case 'branch':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadBranches();
                break;
            case 'vehicleNo':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadVehicleNumbers(currentBranchId);
                break;
            case 'operator':
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                await loadOperators();
                break;
            default:
                setModalData([]);
                setFilteredModalData([]);
        }
    };

    const getLoadingMessage = (type) => {
        switch (type) {
            case 'branch':
                return 'Loading branches...';
            case 'vehicleNo':
                return 'Loading vehicles...';
            case 'operator':
                return 'Loading operators...';
            default:
                return 'Loading data...';
        }
    };

    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'branch':
                console.log('🟢 Setting branch:', item.branchName, 'with ID:', item.branchID);
                
                setCurrentBranch(item);
                setCurrentBranchId(item.branchID);
                
                // Save to AsyncStorage for persistence
                AsyncStorage.setItem('selectedBranch', JSON.stringify(item));
                
                // Clear vehicle and operator data when branch changes
                setVehicleOptions([]);
                setOperatorOptions([]);
                setVehicleNo('');
                setSelectedOperator('');
                setSelectedOperatorId('');
                
                // Load new data for the selected branch
                if (item.branchID) {
                    loadVehicleNumbers(item.branchID);
                    loadOperators();
                }
                break;
            case 'vehicleNo':
                setVehicleNo(item.VehicleId);
                break;
            case 'operator':
                setSelectedOperator(item.UserName);
                setSelectedOperatorId(item.UserId);
                break;
        }
        setModalVisible(false);
    };

    const addOperator = () => {
        if (!selectedOperator) {
            Alert.alert('Validation Error', 'Please select an Operator');
            return;
        }

        if (!vehicleNo) {
            Alert.alert('Validation Error', 'Please select a Vehicle Number');
            return;
        }

        if (!startReading || !endReading) {
            Alert.alert('Validation Error', 'Please enter Start and End Reading');
            return;
        }

        const newOperator = {
            lineNumber: operatorsList.length + 1,
            operatorName: selectedOperator,
            operatorId: selectedOperatorId,
            vehicleNo: vehicleNo,
            startReading: startReading,
            endReading: endReading,
            remarks: remarks || ""
        };

        setOperatorsList([...operatorsList, newOperator]);

        setSelectedOperator('');
        setSelectedOperatorId('');
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setRemarks('');
    };

    const renderModalItem = (item, index) => {
        let displayText = '';

        if (modalType === 'branch') {
            displayText = item.branchName || 'Branch';
        } else if (modalType === 'operator' && item.UserName) {
            displayText = item.UserName;
        } else if (item.VehicleId) {
            displayText = item.VehicleId;
        }

        return (
            <TouchableOpacity
                key={index}
                style={styles.modalItem}
                onPress={() => handleModalItemSelect(item)}
            >
                <Text style={styles.modalItemText}>{displayText}</Text>
            </TouchableOpacity>
        );
    };

    return (
        <View style={styles.container}>
            <Navbar />

            <ScrollOptionsBar
                title="Trips"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <TouchableOpacity 
                style={styles.branchHeader}
                onPress={() => openModal('branch')}
            >
                <Text style={styles.branchHeaderText}>
                    Branch: {getBranchName()} ({currentBranchId || 'Tap to Select'})
                </Text>
                <Text style={styles.branchSubText}>Tap to change branch</Text>
            </TouchableOpacity>

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Entry</Text>

                    <View style={styles.formRow}>
                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Vehicle No."
                                placeholderTextColor="#888"
                                value={vehicleNo}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('vehicleNo')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Operator"
                                placeholderTextColor="#888"
                                value={selectedOperator}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('operator')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.readingInput}
                            placeholder="Start Reading"
                            placeholderTextColor="#888"
                            value={startReading}
                            onChangeText={setStartReading}
                            keyboardType="numeric"
                        />

                        <TextInput
                            style={styles.readingInput}
                            placeholder="End Reading"
                            placeholderTextColor="#888"
                            value={endReading}
                            onChangeText={setEndReading}
                            keyboardType="numeric"
                            disabled
                        />
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Add Remarks"
                            placeholderTextColor="#888"
                            multiline
                            numberOfLines={3}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                    </View>

                    <View style={styles.buttonRow}>
                        <TouchableOpacity style={styles.addButton} onPress={addOperator}>
                            <Text style={styles.addButtonText}>Add Entry</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearButton} onPress={handleClearTripDetails}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Entries</Text>

                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <View style={styles.checkboxCell}>
                                <CheckBox
                                    value={!!isSelectAllOperators}
                                    onValueChange={setIsSelectAllOperators}
                                    tintColors={{ true: 'white', false: 'white' }}
                                    style={styles.headerCheckbox}
                                />
                            </View>
                            <View style={styles.lineNumberCell}>
                                <Text style={styles.tableHeaderText}>#</Text>
                            </View>
                            <View style={styles.operatorNameCell}>
                                <Text style={styles.tableHeaderText}>Operator</Text>
                            </View>
                            <View style={styles.vehicleCell}>
                                <Text style={styles.tableHeaderText}>Vehicle</Text>
                            </View>
                            <View style={styles.readingCell}>
                                <Text style={styles.tableHeaderText}>Readings</Text>
                            </View>
                        </View>

                        <ScrollView style={styles.tableBody} contentContainerStyle={{ flexGrow: 1 }}>
                            {operatorsList.length > 0 ? (
                                operatorsList.map((operator, index) => (
                                    <View key={index} style={styles.tableRow}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.lineNumberCell}>
                                            <Text style={styles.tableCell}>{operator.lineNumber}</Text>
                                        </View>
                                        <View style={styles.operatorNameCell}>
                                            <Text style={styles.tableCell}>{operator.operatorName}</Text>
                                        </View>
                                        <View style={styles.vehicleCell}>
                                            <Text style={styles.tableCell}>{operator.vehicleNo}</Text>
                                        </View>
                                        <View style={styles.readingCell}>
                                            <Text style={styles.tableCell}>{operator.startReading}-{operator.endReading}</Text>
                                        </View>
                                    </View>
                                ))
                            ) : (
                                <View style={styles.emptyTableRow}>
                                    <Text style={styles.emptyTableText}>No Entries</Text>
                                </View>
                            )}
                        </ScrollView>
                    </View>

                    {operatorsList.length > 0 && (
                        <View style={styles.buttonRow}>
                            <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                                <Text style={styles.deleteButtonText}>Delete Selected</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </ScrollView>

            {loading && !modalVisible && (
                <CustomLoader
                    isVisible={loading}
                    message="Loading data..."
                    size="large"
                    color="#02096A"
                    backgroundColor="rgba(0, 0, 0, 0.5)"
                    containerStyle={styles.globalLoaderContainer}
                />
            )}

            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'branch' ? 'Select Branch' :
                                modalType === 'vehicleNo' ? 'Select Vehicle No.' :
                                modalType === 'operator' ? 'Select Operator' : 'Select Item'}
                        </Text>

                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search..."
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>

                        <CustomLoader
                            isVisible={loading}
                            message={getLoadingMessage(modalType)}
                            size="large"
                            color="#02096A"
                            backgroundColor="rgba(255, 255, 255, 0.95)"
                        />

                        {!loading && (
                            <ScrollView>
                                <View style={styles.modalItemsContainer}>
                                    {filteredModalData.length > 0 ? (
                                        filteredModalData.map((item, index) => renderModalItem(item, index))
                                    ) : (
                                        <View style={styles.noDataContainer}>
                                            <Text style={styles.noDataText}>No data available</Text>
                                        </View>
                                    )}
                                </View>
                            </ScrollView>
                        )}

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    contentContainer: {
        flex: 1,
    },
    branchHeader: {
        backgroundColor: '#02096A',
        paddingVertical: 12,
        paddingHorizontal: 15,
        marginHorizontal: 8,
        marginVertical: 4,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: '#FDC500',
    },
    branchHeaderText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        textAlign: 'center',
    },
    branchSubText: {
        color: '#FDC500',
        fontSize: 12,
        fontFamily: 'Poppins',
        textAlign: 'center',
        marginTop: 2,
    },
    sectionContainer: {
        backgroundColor: '#EBEBEB',
        borderRadius: 10,
        padding: 15,
        marginHorizontal: 8,
        marginVertical: 4,
    },
    sectionTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
        marginBottom: 15,
        color: '#02096A',
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 15,
    },
    inputWithButtonContainer: {
        flex: 1,
        flexDirection: 'row',
        marginRight: 10,
        marginBottom: 5,
    },
    inputWithButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 50,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: '700',
    },
    selectButton: {
        backgroundColor: '#041C44',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
    },
    selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    readingInput: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        height: 50,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#333',
        borderWidth: 1,
        borderColor: '#ccc',
        marginRight: 10,
        fontWeight: 'bold',
    },
    remarksInput: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        height: 80,
        textAlignVertical: 'top',
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        borderWidth: 1,
        borderColor: '#ccc',
        fontWeight: 'bold',
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        gap: 15,
        marginTop: 15,
    },
    addButton: {
        backgroundColor: '#02720F',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    clearButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    clearButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    tableContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginTop: 15,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#041C44',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 10,
        height: 45,
    },
    tableHeaderText: {
        fontWeight: 'bold',
        fontSize: 14,
        color: 'white',
        fontFamily: 'Poppins',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 10,
        height: 45,
    },
    tableCell: {
        fontSize: 14,
        color: '#333',
        fontFamily: 'Poppins',
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    lineNumberCell: {
        width: 50,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    operatorNameCell: {
        flex: 2,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    vehicleCell: {
        flex: 1.5,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    readingCell: {
        flex: 1.5,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    emptyTableRow: {
        padding: 15,
        alignItems:'center',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
    },

    // Modal styles with white background and yellow boxes
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        textAlign: 'center',
        color: '#02096A',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    noDataContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 200,
        width: '100%',
    },
    noDataText: {
        fontSize: 16,
        color: '#999',
        fontFamily: 'Poppins',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    globalLoaderContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    tableBody: {
        height: 200, // or any fixed height you want
    },
});

export default TripRoaster;