import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import Navbar from '../../components/Navbar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchBranchDetails } from '../../apiHandling/ReportAPI/fetchBillReportsAPI';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';

const CreditLedgerReportPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { creditLedgerData, customerInfo, fromDate, toDate } = route.params;

  const [businessDate, setBusinessDate] = useState(null);
  const [branchName, setBranchName] = useState('');
  const [branchId, setBranchId] = useState('');
  const [areaName, setAreaName] = useState('');
  const [pincode, setPincode] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPageData();
  }, []);

  const fetchPageData = async () => {
    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      const branchDetails = await fetchBranchDetails(loginBranchID, bearerToken);
      const dateStr = await fetchBusinessDay(bearerToken, loginBranchID);

      // Parse date from DD-MM-YYYY format
      const parsedDate = dateStr ? new Date(dateStr.split('-').reverse().join('-')) : null;

      setBranchName(branchDetails.BranchName || '');
      setBranchId(branchDetails.BranchId || loginBranchID);
      setAreaName(branchDetails.AreaName || '');
      setPincode(branchDetails.PINCODE || '');
      setBusinessDate(parsedDate);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching report metadata:', error);
      setLoading(false);
    }
  };

  const formatDate = (date) => {
    if (!date) return '';
    const options = { day: '2-digit', month: 'short', year: '2-digit' };
    return date.toLocaleDateString('en-GB', options);
  };

  const formatDateTime = (date) => {
    if (!date) return '';
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };
    return date.toLocaleDateString('en-GB', options).replace(',', '');
  };

  const formatBusinessDate = (dateString) => {
    if (!dateString) return '';
    try {
      // Parse DD-MM-YYYY format
      const [day, month, year] = dateString.split('-');
      const date = new Date(`${year}-${month}-${day}`);
      return formatDate(date);
    } catch (error) {
      return dateString;
    }
  };

  if (loading || !businessDate) {
    return (
      <View style={styles.container}>
        <Navbar />
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </View>
    );
  }

  // Group data by transaction type
  const groupedData = creditLedgerData.reduce((acc, item) => {
    const transType = item.transTypeID || 'UNKNOWN';
    if (!acc[transType]) {
      acc[transType] = [];
    }
    acc[transType].push(item);
    return acc;
  }, {});

  return (
    <View style={styles.container}>
      <Navbar />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
        <Text style={styles.title}>Credit Ledger Report</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Company Header */}
        <Text style={styles.companyName}>ABIS Exports Pvt Ltd</Text>
        <Text style={styles.branchInfo}>{branchName}, {branchId}</Text>
        <Text style={styles.branchInfo}>
          {areaName}{pincode ? `, ${pincode}` : ''}
        </Text>

        <View style={styles.divider} />

        <Text style={styles.reportTitle}>Credit Ledger Report</Text>

        <View style={styles.divider} />

        <Text style={styles.dateInfo}>
          Business Date: {formatDate(businessDate)}
        </Text>
        <Text style={styles.dateInfo}>Print Date: {formatDateTime(new Date())}</Text>

        <View style={styles.thinDivider} />

        {/* Table Header */}
        <View style={styles.tableHeaderRow}>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>ISO Number</Text>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>Amount</Text>
          <Text style={[styles.tableHeaderText, { flex: 2 }]}>Business Date</Text>
        </View>

        <View style={styles.thinDivider} />

        {/* Grouped Data by Transaction Type */}
        {Object.keys(groupedData).map((transType, groupIndex) => (
          <View key={groupIndex}>
            {/* Transaction Type Header */}
            <View style={styles.transTypeHeader}>
              <Text style={styles.transTypeText}>{transType}</Text>
            </View>

            {/* Data for this transaction type */}
            {groupedData[transType].map((item, index) => {
              // Determine amount based on transaction type
              let amount = 0;
              if (item.transTypeID === 'SALE') {
                amount = item.receivable || 0;
              } else if (item.transTypeID === 'SLRETURN') {
                amount = item.cNissued || 0;
              } else if (item.transTypeID === 'RECEIPT') {
                amount = item.receipt || 0;
              }

              return (
                <View key={index} style={styles.tableRow}>
                  <Text style={[styles.tableText, { flex: 2 }]}>
                    {item.isO_Number || ''}
                  </Text>
                  <Text style={[styles.tableText, { flex: 2 }]}>
                    {amount.toFixed(2)}
                  </Text>
                  <Text style={[styles.tableText, { flex: 2 }]}>
                    {formatBusinessDate(item.businessDate)}
                  </Text>
                </View>
              );
            })}
          </View>
        ))}

        <View style={styles.spacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6E6E6',
    paddingVertical: 15,
    paddingHorizontal: 10,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'black',
  },
  content: {
    flex: 1,
    backgroundColor: 'white',
    margin: 10,
    padding: 15,
    borderRadius: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  companyName: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  branchInfo: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 2,
  },
  divider: {
    height: 2,
    backgroundColor: 'black',
    marginVertical: 10,
  },
  thinDivider: {
    height: 1,
    backgroundColor: '#ccc',
    marginVertical: 5,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 5,
  },
  dateInfo: {
    fontSize: 12,
    marginBottom: 2,
  },
  tableHeaderRow: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 5,
  },
  tableHeaderText: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  transTypeHeader: {
    backgroundColor: '#e8e8e8',
    paddingVertical: 6,
    paddingHorizontal: 5,
    marginTop: 5,
  },
  transTypeText: {
    fontSize: 13,
    fontWeight: 'bold',
    color: '#333',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 6,
    paddingHorizontal: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ddd',
  },
  tableText: {
    fontSize: 11,
    textAlign: 'center',
  },
  spacing: {
    height: 20,
  },
});

export default CreditLedgerReportPage;
