import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Modal as RNModal,
    ActivityIndicator,
} from 'react-native';

const Modal = ({
    visible,
    onClose,
    modalType,
    modalData,
    onItemSelect,
    loading = false,
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);

    // Filter modal data when search term changes
    useEffect(() => {
        console.log('🔍 Filtering modal data with search term:', searchTerm);
        if (searchTerm && modalData.length > 0) {
            const filtered = modalData.filter(item => {
                let searchValue = '';
                if (typeof item === 'string') {
                    searchValue = item.toLowerCase();
                } else if (item.TransTypeName) {
                    searchValue = item.TransTypeName.toLowerCase();
                } else if (item.branchName) {
                    searchValue = item.branchName.toLowerCase();
                } else if (item.tripID) {
                    searchValue = item.tripID.toLowerCase();
                } else if (item.DocID) {
                    searchValue = item.DocID.toLowerCase();
                } else if (item.displayText) {
                    searchValue = item.displayText.toLowerCase();
                }
                return searchValue.includes(searchTerm.toLowerCase());
            });
            console.log('🔍 Filtered results count:', filtered.length);
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    // Reset search when modal opens/closes
    useEffect(() => {
        if (visible) {
            setSearchTerm('');
            setFilteredModalData(modalData);
        }
    }, [visible, modalData]);

    /**
     * Gets the appropriate display text for modal items based on type
     */
    const getModalItemDisplayText = (item, modalType) => {
        if (typeof item === 'string') {
            return item;
        }
        
        if (typeof item === 'object') {
            switch (modalType) {
                case 'tripBranch':
                    return item.branchName || item.displayText;
                case 'docType':
                    return item.TransTypeName || item.displayText;
                case 'tripId':
                    return item.tripID || item.displayText;
                case 'selectDoc':
                    return item.DocID || item.displayText;
                case 'place':
                    return item.displayText || item.name || item.title;
                default:
                    return item.displayText || item.name || item.title || 'Unknown';
            }
        }
        return item;
    };

    /**
     * Renders different modal titles based on selection type
     */
    const renderModalTitle = () => {
        switch (modalType) {
            case 'tripBranch': return 'Select Trip Branch';
            case 'tripId': return 'Select Trip ID';
            case 'docType': return 'Select DOC Type';
            case 'selectDoc': return 'Select Sales Document';
            case 'place': return 'Select Place';
            default: return 'Select Item';
        }
    };

    /**
     * Handles item selection and closes modal
     */
    const handleItemSelect = (item) => {
        console.log('🎯 Modal item selected:', item, 'for type:', modalType);
        onItemSelect(item);
        onClose();
    };

    /**
     * Renders trip cards for trip ID selection
     */
    const renderTripCards = () => (
        <View style={styles.tripCardsContainer}>
            {loading ? (
                <View style={styles.emptySearchContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.emptySearchText}>Loading trips...</Text>
                </View>
            ) : filteredModalData.length > 0 ? (
                filteredModalData.map((trip, index) => (
                    <TouchableOpacity
                        key={trip.tripID || index}
                        style={styles.tripCard}
                        onPress={() => handleItemSelect(trip)}
                    >
                        <Text style={styles.tripIdText}>{getModalItemDisplayText(trip, modalType)}</Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>No trips found</Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders sales document cards
     */
    const renderSalesDocCards = () => (
        <View style={styles.tripCardsContainer}>
            {loading ? (
                <View style={styles.emptySearchContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.emptySearchText}>Loading sales documents...</Text>
                </View>
            ) : filteredModalData.length > 0 ? (
                filteredModalData.map((doc, index) => (
                    <TouchableOpacity
                        key={doc.DocID || index}
                        style={styles.salesDocCard}
                        onPress={() => handleItemSelect(doc)}
                    >
                        <Text style={styles.salesDocText}>{getModalItemDisplayText(doc, modalType)}</Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>No sales documents found</Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders regular modal items grid
     */
    const renderRegularItems = () => (
        <View style={styles.modalItemsContainer}>
            {filteredModalData.length > 0 ? (
                filteredModalData.map((item, index) => (
                    <TouchableOpacity
                        key={index}
                        style={styles.modalItem}
                        onPress={() => handleItemSelect(item)}
                    >
                        <Text style={styles.modalItemText}>
                            {getModalItemDisplayText(item, modalType)}
                        </Text>
                    </TouchableOpacity>
                ))
            ) : (
                <View style={styles.emptySearchContainer}>
                    <Text style={styles.emptySearchText}>
                        {loading ? 'Loading...' : 'No items found'}
                    </Text>
                </View>
            )}
        </View>
    );

    /**
     * Renders modal content based on type
     */
    const renderModalContent = () => {
        switch (modalType) {
            case 'tripId':
                return renderTripCards();
            case 'selectDoc':
                return renderSalesDocCards();
            default:
                return renderRegularItems();
        }
    };

    return (
        <RNModal
            animationType="fade"
            transparent={true}
            visible={visible}
            onRequestClose={onClose}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <Text style={styles.modalTitle}>
                        {renderModalTitle()}
                    </Text>

                    {/* Search input */}
                    <View style={styles.searchContainer}>
                        <TextInput
                            style={styles.searchInput}
                            placeholder="Search"
                            placeholderTextColor="#888"
                            value={searchTerm}
                            onChangeText={setSearchTerm}
                        />
                    </View>

                    {/* Modal content */}
                    <ScrollView style={styles.modalScrollContainer}>
                        {renderModalContent()}
                    </ScrollView>

                    <TouchableOpacity
                        style={styles.modalCloseButton}
                        onPress={onClose}
                    >
                        <Text style={styles.modalCloseButtonText}>Close</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </RNModal>
    );
};

const styles = StyleSheet.create({
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    modalScrollContainer: {
        maxHeight: 400,
    },
    
    // Trip Cards Container
    tripCardsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    tripCard: {
        backgroundColor: '#FDC500', // Yellow background
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    tripIdText: {
        fontSize: 15,
        color: '#333', // Black text
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Sales Document Cards
    salesDocCard: {
        backgroundColor: '#FDC500', // Yellow background
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    salesDocText: {
        fontSize: 15,
        color: '#333', // Black text
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Regular Modal Items
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    
    // Empty States
    emptySearchContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    emptySearchText: {
        textAlign: 'center',
        fontFamily: 'Poppins',
        color: '#666',
        marginTop: 20,
        marginBottom: 20,
        fontStyle: 'italic',
    },
    
    // Close Button
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
});

export default Modal;