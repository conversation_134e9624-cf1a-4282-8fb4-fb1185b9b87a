import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, FlatList,  StyleSheet } from 'react-native';
// import { Ionicons, FontAwesome } from '@expo/vector-icons';
import CheckBox from '@react-native-community/checkbox';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import DateTimePicker from '@react-native-community/datetimepicker';
import Navbar from '../../components/Navbar';

const LeaveScreen = () => {
  const [employeeName, setEmployeeName] = useState('');
  const [allocationType, setAllocationType] = useState('');
  const [remarks, setRemarks] = useState('');
  const [fromDate, setFromDate] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [showFromDate, setShowFromDate] = useState(false);
  const [showToDate, setShowToDate] = useState(false);
  const [items, setItems] = useState([]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <Navbar />
      <View style={styles.header}>
        <Text style={styles.leaveTitle}>LEAVE</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity style={styles.headerBtn}><Text>New 👤</Text></TouchableOpacity>
          <TouchableOpacity style={styles.headerBtn}><Text>View 👁️</Text></TouchableOpacity>
          <TouchableOpacity style={styles.saveBtn}><Text style={styles.btnText}>Save</Text></TouchableOpacity>
          <TouchableOpacity style={styles.cancelBtn}><Text style={styles.btnText}>Cancel</Text></TouchableOpacity>
        </View>
      </View>

      {/* Input Row */}
      <View style={styles.inputRow}>
        <TextInput
          placeholder="Employee Name"
          style={styles.input}
          value={employeeName}
          onChangeText={setEmployeeName}
        />
        <TouchableOpacity style={styles.searchBtn}><FontAwesome name="search" size={20} color="navy" /></TouchableOpacity>

        <TextInput
          placeholder="Select Allocation Type"
          style={styles.input}
          value={allocationType}
          onChangeText={setAllocationType}
        />
      </View>

      {/* Date Picker Row */}
      <View style={styles.dateRow}>
        <TouchableOpacity onPress={() => setShowFromDate(true)} style={styles.dateBox}>
          <Text style={styles.dateText}>{fromDate.toDateString()}</Text>
          <FontAwesome name="calendar" size={18} color="black" />
        </TouchableOpacity>
        <Text style={{ marginHorizontal: 10 }}>To</Text>
        <TouchableOpacity onPress={() => setShowToDate(true)} style={styles.dateBox}>
          <Text style={styles.dateText}>{toDate.toDateString()}</Text>
          <FontAwesome name="calendar" size={18} color="black" />
        </TouchableOpacity>
      </View>

      {/* Date Pickers */}
      {showFromDate && (
        <DateTimePicker
          value={fromDate}
          mode="date"
          display="default"
          onChange={(e, selectedDate) => {
            setShowFromDate(false);
            if (selectedDate) setFromDate(selectedDate);
          }}
        />
      )}
      {showToDate && (
        <DateTimePicker
          value={toDate}
          mode="date"
          display="default"
          onChange={(e, selectedDate) => {
            setShowToDate(false);
            if (selectedDate) setToDate(selectedDate);
          }}
        />
      )}

      {/* Remarks Box */}
      <TextInput
        placeholder="Add Remarks"
        style={styles.remarksBox}
        value={remarks}
        onChangeText={setRemarks}
        multiline
      />

      {/* Action Buttons */}
      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.addBtn}><Text style={styles.btnText}>Add</Text></TouchableOpacity>
        <TouchableOpacity style={styles.clearBtn}><Text style={styles.btnText}>Clear</Text></TouchableOpacity>
      </View>

      {/* Table Headers */}
      <View style={styles.tableHeader}>
        <CheckBox value={false} />
        <Text style={styles.tableText}>LineNumber</Text>
        <Text style={styles.tableText}>ItemName</Text>
        <Text style={styles.tableText}>Nos</Text>
        <Text style={styles.tableText}>Kgs</Text>
        <Text style={styles.tableText}>Remarks</Text>
      </View>

      {/* Delete Button */}
      <TouchableOpacity style={styles.deleteBtn}>
        <Text style={styles.btnText}>Delete Selected Row</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1,  backgroundColor: '#fff' },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
  leaveTitle: { fontSize: 20, fontWeight: 'bold' },
  headerButtons: { flexDirection: 'row', gap: 10 },
  headerBtn: { padding: 8, backgroundColor: '#eee', borderRadius: 5 },
  saveBtn: { padding: 8, backgroundColor: 'green', borderRadius: 5 },
  cancelBtn: { padding: 8, backgroundColor: 'red', borderRadius: 5 },
  btnText: { color: 'white', fontWeight: 'bold' },
  inputRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10 },
  input: { flex: 1, borderWidth: 1, borderColor: '#ccc', borderRadius: 8, padding: 10, marginHorizontal: 5 },
  searchBtn: { padding: 10, backgroundColor: '#FFD700', borderRadius: 6 },
  dateRow: { flexDirection: 'row', alignItems: 'center', marginVertical: 10 },
  dateBox: { flexDirection: 'row', alignItems: 'center', padding: 10, borderRadius: 6, backgroundColor: '#001F54', color: 'white' },
  dateText: { color: 'white', marginRight: 8 },
  remarksBox: { borderWidth: 1, borderColor: '#ccc', borderRadius: 8, padding: 10, marginBottom: 10, height: 60 },
  buttonRow: { flexDirection: 'row', justifyContent: 'space-around', marginVertical: 10 },
  addBtn: { backgroundColor: 'green', padding: 12, borderRadius: 20 },
  clearBtn: { backgroundColor: 'red', padding: 12, borderRadius: 20 },
  tableHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', backgroundColor: '#eee', padding: 10, borderRadius: 5 },
  tableText: { flex: 1, fontWeight: 'bold', textAlign: 'center' },
  deleteBtn: { backgroundColor: 'red', padding: 12, borderRadius: 8, alignItems: 'center', marginTop: 10 },
});

export default LeaveScreen;
