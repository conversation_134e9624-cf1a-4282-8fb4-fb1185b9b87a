import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    FlatList,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Calendar } from 'react-native-calendars';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Navbar from '../../components/Navbar';
import ScrollOptionsBar from '../../components/ScrollOptionsBar';

/**
 * ReceiptForSubscription Component
 * 
 * A comprehensive interface for managing subscription receipts.
 * Features include receipt details management, item addition, and tabular display.
 */
export const MiscCollection = () => {
    // Navigation and form states
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    
    // Receipt header information
    const [docNumber, setDocNumber] = useState('');
    const [businessDate, setBusinessDate] = useState('18-05-2025');
    const [mobile, setMobile] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [remarks, setRemarks] = useState('');
    
    // Payment details
    const [receiptMode, setReceiptMode] = useState('');
    const [chequeNumber, setChequeNumber] = useState('');
    const [bankName, setBankName] = useState('');
    const [chequeDate, setChequeDate] = useState('03-06-2025');
    const [paymentAmount, setPaymentAmount] = useState('');
    
    // Data table states
    const [items, setItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [isSelectAllItems, setIsSelectAllItems] = useState(false);

    // Modal states for selection interfaces
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    
    // Calendar modal state
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [selectedDateField, setSelectedDateField] = useState('');
    
    // Customer modal state
    const [customerModalVisible, setCustomerModalVisible] = useState(false);
    const [customerSearchTerm, setCustomerSearchTerm] = useState('');
    const [filteredCustomers, setFilteredCustomers] = useState([]);

    // Dropdown options
    const receiptModeOptions = ['Cash', 'Card', 'Cheque', 'Online Transfer', 'UPI'];
    
    // Customer data
    const customerData = [
        'John Doe',
        'Jane Smith', 
        'Mike Johnson',
        'Sarah Williams',
        'David Brown',
        'Lisa Davis',
        'Robert Wilson',
        'Emma Thompson',
        'James Taylor',
        'Mary Anderson'
    ];

    // Filter modal data when search term changes
    useEffect(() => {
        if (searchTerm) {
            const filtered = modalData.filter(item => 
                item.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);
    
    // Filter customer data when search term changes
    useEffect(() => {
        if (customerSearchTerm) {
            const filtered = customerData.filter(customer => 
                customer.toLowerCase().includes(customerSearchTerm.toLowerCase())
            );
            setFilteredCustomers(filtered);
        } else {
            setFilteredCustomers(customerData);
        }
    }, [customerSearchTerm]);

    // Handle select all checkbox for items
    useEffect(() => {
        if (isSelectAllItems) {
            const allIndices = items.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllItems]);

    /**
     * Handles actions from the ScrollOptionsBar
     */
    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        
        switch(option) {
            case 'New':
                resetForm();
                break;
            case 'Save':
                saveReceipt();
                break;
            case 'View':
                // View logic would be implemented here
                break;
            case 'Cancel':
                resetForm();
                break;
            case 'Post':
                postReceipt();
                break;
            default:
                break;
        }
    };

    /**
     * Resets all form fields to their initial state
     */
    const resetForm = () => {
        setDocNumber('');
        setBusinessDate('18-05-2025');
        setMobile('');
        setCustomerName('');
        setRemarks('');
        setReceiptMode('');
        setChequeNumber('');
        setBankName('');
        setChequeDate('03-06-2025');
        setPaymentAmount('');
        setItems([]);
        setSelectedRows([]);
        setIsSelectAllItems(false);
    };

    /**
     * Validates and saves the receipt
     */
    const saveReceipt = () => {
        if (!customerName) {
            Alert.alert('Validation Error', 'Please enter customer name');
            return;
        }
        Alert.alert('Success', 'Receipt saved successfully');
    };

    /**
     * Posts the receipt
     */
    const postReceipt = () => {
        if (!customerName) {
            Alert.alert('Validation Error', 'Please enter customer name');
            return;
        }
        Alert.alert('Success', 'Receipt posted successfully');
    };

    /**
     * Toggles selection of a table row
     */
    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllItems(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            if (selectedRows.length + 1 === items.length) {
                setIsSelectAllItems(true);
            }
        }
    };

    /**
     * Removes selected rows from the items table
     */
    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }
        const newItems = items.filter((_, index) => !selectedRows.includes(index));
        setItems(newItems);
        setSelectedRows([]);
        setIsSelectAllItems(false);
    };

    /**
     * Opens selection modal with appropriate data
     */
    const openModal = (type) => {
        setSearchTerm('');
        
        let dataToShow = [];
        switch (type) {
            case 'receiptMode':
                dataToShow = receiptModeOptions;
                break;
            default:
                dataToShow = [];
        }
        
        setModalData(dataToShow);
        setFilteredModalData(dataToShow);
        setModalType(type);
        setModalVisible(true);
    };

    /**
     * Handles item selection from modal
     */
    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'receiptMode':
                setReceiptMode(item);
                break;
            default:
                break;
        }
        setModalVisible(false);
    };
    
    /**
     * Opens calendar modal for date selection
     */
    const openCalendar = (field) => {
        setSelectedDateField(field);
        setCalendarVisible(true);
    };
    
    /**
     * Handles date selection from calendar
     */
    const handleDateSelect = (day) => {
        const formattedDate = day.dateString.split('-').reverse().join('-'); // Convert YYYY-MM-DD to DD-MM-YYYY
        if (selectedDateField === 'businessDate') {
            setBusinessDate(formattedDate);
        } else if (selectedDateField === 'chequeDate') {
            setChequeDate(formattedDate);
        }
        setCalendarVisible(false);
    };
    
    /**
     * Opens customer selection modal
     */
    const openCustomerModal = () => {
        setCustomerSearchTerm('');
        setFilteredCustomers(customerData);
        setCustomerModalVisible(true);
    };
    
    /**
     * Handles customer selection
     */
    const handleCustomerSelect = (customer) => {
        setCustomerName(customer);
        setCustomerModalVisible(false);
    };
    
    /**
     * Handles customer search
     */
    const handleCustomerSearch = () => {
        const filtered = customerData.filter(customer => 
            customer.toLowerCase().includes(customerSearchTerm.toLowerCase())
        );
        setFilteredCustomers(filtered);
    };

    /**
     * Get current date in YYYY-MM-DD format for calendar
     */
    const getCurrentDate = () => {
        const today = new Date();
        return today.toISOString().split('T')[0];
    };

    /**
     * Convert DD-MM-YYYY to YYYY-MM-DD for calendar
     */
    const convertDateForCalendar = (dateString) => {
        if (!dateString) return getCurrentDate();
        const parts = dateString.split('-');
        if (parts.length === 3) {
            return `${parts[2]}-${parts[1]}-${parts[0]}`;
        }
        return getCurrentDate();
    };

    /**
     * Handle Add button press
     */
    const handleAddItem = () => {
        // Add item logic here
        Alert.alert('Info', 'Add item functionality');
    };

    return (
        <View style={styles.container}>
            <Navbar />
            
            <ScrollOptionsBar
                title="Misc Collection"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <ScrollView style={styles.contentContainer}>
                <View style={styles.sectionContainer}>
                    {/* First Row: Doc# and Business Date */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Doc#</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={docNumber}
                                onChangeText={setDocNumber}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Business Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder=""
                                    placeholderTextColor="#888"
                                    value={businessDate}
                                    onChangeText={setBusinessDate}
                                    editable={false}
                                />
                                <TouchableOpacity 
                                    style={styles.calendarButton}
                                    onPress={() => openCalendar('businessDate')}
                                >
                                    <Icon name="date-range" size={16} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Second Row: Line# and Remarks */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Line#</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={mobile}
                                onChangeText={setMobile}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Remarks</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={remarks}
                                onChangeText={setRemarks}
                            />
                        </View>
                    </View>

                    {/* Third Row: Payment Details */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft No/Ref No</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={chequeNumber}
                                onChangeText={setChequeNumber}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Bank Name</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={bankName}
                                onChangeText={setBankName}
                            />
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Cheque/Draft Date</Text>
                            <View style={styles.dateInputContainer}>
                                <TextInput
                                    style={styles.dateInput}
                                    placeholder=""
                                    placeholderTextColor="#888"
                                    value={chequeDate}
                                    onChangeText={setChequeDate}
                                    editable={false}
                                />
                                <TouchableOpacity 
                                    style={styles.calendarButton}
                                    onPress={() => openCalendar('chequeDate')}
                                >
                                    <Icon name="date-range" size={16} color="white" />
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>

                    {/* Fourth Row: Payment Mode and Amount with Add Button */}
                    <View style={styles.formRow}>
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Payment Mode</Text>
                            <TouchableOpacity 
                                style={styles.dropdown}
                                onPress={() => openModal('receiptMode')}
                            >
                                <Text style={[styles.inputText, !receiptMode && styles.placeholderText]}>
                                    {receiptMode || ''}
                                </Text>
                                <Text style={styles.dropdownIcon}>▼</Text>
                            </TouchableOpacity>
                        </View>
                        
                        <View style={styles.inputContainer}>
                            <Text style={styles.labelText}>Amount</Text>
                            <TextInput
                                style={styles.input}
                                placeholder=""
                                placeholderTextColor="#888"
                                value={paymentAmount}
                                onChangeText={setPaymentAmount}
                                keyboardType="numeric"
                            />
                        </View>
                        
                        <View style={styles.addButtonContainer}>
                            <TouchableOpacity style={styles.addButton} onPress={handleAddItem}>
                                <Text style={styles.addButtonText}>Add</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Items Table */}
                    <View style={styles.tableContainer}>
                        {items.length > 0 ? (
                            <>
                                <View style={styles.tableHeader}>
                                    <View style={styles.checkboxCell}>
                                        <CheckBox
                                            value={isSelectAllItems}
                                            onValueChange={setIsSelectAllItems}
                                            tintColors={{ true: '#FDC500', false: '#FDC500' }}
                                        />
                                    </View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Item</Text></View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Amount</Text></View>
                                    <View style={styles.tableHeaderCell}><Text style={styles.tableHeaderText}>Date</Text></View>
                                </View>

                                {items.map((item, index) => (
                                    <View key={index} style={styles.tableRow}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.name}</Text></View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.amount}</Text></View>
                                        <View style={styles.tableCell}><Text style={styles.tableCellText}>{item.date}</Text></View>
                                    </View>
                                ))}
                            </>
                        ) : (
                            <View style={styles.emptyTableContainer}>
                                <Text style={styles.emptyTableText}>No Data</Text>
                            </View>
                        )}
                    </View>

                    {/* Delete Selected Button */}
                    <View style={styles.deleteButtonContainer}>
                        <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                            <Text style={styles.deleteButtonText}>Delete selected</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>

            {/* Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'receiptMode' ? 'Select Payment Mode' : 'Select Option'}
                        </Text>
                        
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search"
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>
                        
                        <ScrollView>
                            <View style={styles.modalItemsContainer}>
                                {filteredModalData.map((item, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.modalItem}
                                        onPress={() => handleModalItemSelect(item)}
                                    >
                                        <Text style={styles.modalItemText}>{item}</Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Customer Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={customerModalVisible}
                onRequestClose={() => setCustomerModalVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setCustomerModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Select Customer</Text>
                        
                        <View style={styles.customerSearchContainer}>
                            <TextInput
                                style={styles.customerSearchInput}
                                placeholder="Search Customer"
                                placeholderTextColor="#888"
                                value={customerSearchTerm}
                                onChangeText={setCustomerSearchTerm}
                            />
                            <TouchableOpacity 
                                style={styles.searchButton}
                                onPress={handleCustomerSearch}
                            >
                                <Text style={styles.searchButtonText}>Search</Text>
                            </TouchableOpacity>
                        </View>
                        
                        <ScrollView>
                            <View style={styles.customerItemsContainer}>
                                {filteredCustomers.map((customer, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={styles.customerItem}
                                        onPress={() => handleCustomerSelect(customer)}
                                    >
                                        <Text style={styles.customerItemText}>{customer}</Text>
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </ScrollView>
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCustomerModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Calendar Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={calendarVisible}
                onRequestClose={() => setCalendarVisible(false)}
            >
                <TouchableOpacity 
                    style={styles.modalOverlay} 
                    activeOpacity={1} 
                    onPress={() => setCalendarVisible(false)}
                >
                    <View style={styles.calendarModalContent}>
                        <Text style={styles.modalTitle}>Select Date</Text>
                        
                        <Calendar
                            onDayPress={handleDateSelect}
                            markedDates={{
                                [convertDateForCalendar(selectedDateField === 'businessDate' ? businessDate : chequeDate)]: {
                                    selected: true,
                                    selectedColor: '#FDC500'
                                }
                            }}
                            theme={{
                                backgroundColor: '#ffffff',
                                calendarBackground: '#ffffff',
                                textSectionTitleColor: '#02096A',
                                selectedDayBackgroundColor: '#FDC500',
                                selectedDayTextColor: '#000000',
                                todayTextColor: '#02096A',
                                dayTextColor: '#2d4150',
                                textDisabledColor: '#d9e1e8',
                                dotColor: '#FDC500',
                                selectedDotColor: '#ffffff',
                                arrowColor: '#02096A',
                                disabledArrowColor: '#d9e1e8',
                                monthTextColor: '#02096A',
                                indicatorColor: '#02096A',
                                textDayFontFamily: 'Poppins',
                                textMonthFontFamily: 'Poppins',
                                textDayHeaderFontFamily: 'Poppins',
                                textDayFontWeight: 'bold',
                                textMonthFontWeight: 'bold',
                                textDayHeaderFontWeight: 'bold',
                                textDayFontSize: 14,
                                textMonthFontSize: 16,
                                textDayHeaderFontSize: 12
                            }}
                        />
                        
                        <TouchableOpacity 
                            style={styles.modalCloseButton}
                            onPress={() => setCalendarVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    contentContainer: {
        flex: 1,
        padding: 10,
    },
    sectionContainer: {
        backgroundColor: '#EBEBEB',
        borderRadius: 10,
        padding: 15,
        marginHorizontal: 8,
        marginVertical: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        marginBottom: 15,
        gap: 15,
    },
    inputContainer: {
        flex: 1,
        minWidth: 120,
    },
    addButtonContainer: {
        justifyContent: 'flex-end',
        alignItems: 'center',
        minWidth: 80,
    },
    labelText: {
        fontSize: 11,
        color: '#333',
        marginBottom: 5,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    input: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 6,
        padding: 10,
        height: 38,
        fontFamily: 'Poppins',
        fontSize: 13,
        color: '#333',
        fontWeight: 'bold',
    },
    dateInputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    dateInput: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 6,
        padding: 10,
        height: 38,
        fontFamily: 'Poppins',
        fontSize: 13,
        color: '#333',
        fontWeight: 'bold',
        marginRight: 5,
    },
    calendarButton: {
        backgroundColor: '#02096A',
        borderRadius: 6,
        padding: 10,
        height: 38,
        width: 38,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    dropdown: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 6,
        padding: 10,
        height: 38,
    },
    inputText: {
        color: '#333',
        fontFamily: 'Poppins',
        fontSize: 13,
        fontWeight: 'bold',
    },
    placeholderText: {
        color: '#888',
        fontWeight: 'bold',
    },
    dropdownIcon: {
        color: '#333',
        fontSize: 14,
        fontWeight: 'bold',
    },
    addButton: {
        backgroundColor: '#02096A',
        paddingVertical: 8,
        paddingHorizontal: 20,
        borderRadius: 6,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 38,
        minWidth: 70,
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        fontFamily: 'Poppins',
    },
    tableContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        borderWidth: 1,
        borderColor: '#ddd',
        minHeight: 200,
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#041C44',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableHeaderText: {
        fontWeight: 'bold',
        fontSize: 12,
        color: 'white',
        fontFamily: 'Poppins',
        textAlign: 'center',
    },
    tableHeaderCell: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 8,
        minHeight: 40,
    },
    tableCell: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    tableCellText: {
        fontSize: 12,
        color: '#333',
        fontFamily: 'Poppins',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableContainer: {
        padding: 40,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8f8f8',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
        fontWeight: 'bold',
        fontStyle: 'italic',
    },
    deleteButtonContainer: {
        alignItems: 'flex-start',
        marginTop: 10,
    },
    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 6,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 13,
        fontFamily: 'Poppins',
    },
    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    // Customer Modal Styles
    customerSearchContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerSearchInput: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
        fontWeight: 'bold',
    },
    searchButton: {
        backgroundColor: '#02096A',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    searchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        fontSize: 14,
    },
    customerItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    customerItem: {
        backgroundColor: '#FDC500',
        width: 140,
        height: 60,
        padding: 10,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    customerItemText: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    // Calendar Modal Styles
    calendarModalContent: {
        width: '90%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
})

export default MiscCollection;