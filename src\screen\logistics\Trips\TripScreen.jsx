import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    FlatList,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';
import CustomLoader from '../../../components/CustomLoader';
import { local_signage, local_url } from '../../../../const';

export const TripScreen = () => {
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [isOwnBranchVehicle, setIsOwnBranchVehicle] = useState(true);
    const [selectedTripType, setSelectedTripType] = useState('');
    const [selectedTripTypeCode, setSelectedTripTypeCode] = useState('');
    const [vehicleBranch, setVehicleBranch] = useState('');
    const [vehicleNo, setVehicleNo] = useState('');
    const [startReading, setStartReading] = useState('');
    const [endReading, setEndReading] = useState('');
    const [destination, setDestination] = useState('');
    const [remarks, setRemarks] = useState('');
    const [operatorType, setOperatorType] = useState('');
    const [selectedOperator, setSelectedOperator] = useState('');
    const [selectedOperatorId, setSelectedOperatorId] = useState('');
    const [selectedRows, setSelectedRows] = useState([]);
    const [operatorsList, setOperatorsList] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredModalData, setFilteredModalData] = useState([]);
    const [isSelectAllOperators, setIsSelectAllOperators] = useState(false);
    const [authToken, setAuthToken] = useState('');
    const [currentBranchId, setCurrentBranchId] = useState('');
    const [currentBranch, setCurrentBranch] = useState(null);
    const [loading, setLoading] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);
    const [selectedOperatorDesigCode, setSelectedOperatorDesigCode] = useState('');

    // API Data states
    const [tripTypeOptions, setTripTypeOptions] = useState([]);
    const [vehicleOptions, setVehicleOptions] = useState([]);
    const [operatorTypeOptions, setOperatorTypeOptions] = useState([]);
    const [operatorOptions, setOperatorOptions] = useState([]);

    // Static data that doesn't have API endpoints
    // const destinationOptions = ['Chennai', 'Bangalore', 'Hyderabad', 'Mumbai', 'Delhi', 'Kolkata'];

    // Get branch options - use the selected branch as the primary option
    const getBranchOptions = () => {
        console.log('🏢 Getting branch options...');
        console.log('🏢 Current branch object:', currentBranch);

        if (currentBranch) {
            // Try different possible name properties
            const branchName = currentBranch.name ||
                currentBranch.branchName ||
                currentBranch.BranchName ||
                currentBranch.displayName ||
                currentBranch.label ||
                'Selected Branch';

            console.log('✅ Using selected branch:', branchName);
            return [branchName];
        }

        // Fallback options if no selected branch
        console.warn('⚠️ No selected branch found, using fallback options');
        return ['Ajni', 'Abis', 'Laziz Shop'];
    };

    useEffect(() => {
        // Get auth token from AsyncStorage first
        const initializeApp = async () => {
            await getAuthToken();
        };
        initializeApp();
    }, []);

    useEffect(() => {
        // Load initial data only after auth token and branch are available
        if (authToken && currentBranchId) {
            console.log('🚀 Prerequisites met - loading initial data...');
            console.log('🚀 Auth Token:', authToken ? 'Available' : 'Missing');
            console.log('🚀 Current Branch ID:', currentBranchId);
            console.log('🚀 Current Branch Object:', currentBranch);

            loadTripTypes();
            loadOperatorTypes();
        } else {
            console.log('⏳ Waiting for prerequisites...');
            console.log('⏳ Auth Token:', authToken ? 'Available' : 'Missing');
            console.log('⏳ Current Branch ID:', currentBranchId || 'Missing');
        }
    }, [authToken, currentBranchId]);

    // Add a separate effect to monitor currentBranch changes
    useEffect(() => {
        console.log('🔄 Current branch changed:', currentBranch);
        console.log('🔄 Current branch ID changed:', currentBranchId);
    }, [currentBranch, currentBranchId]);

    useEffect(() => {
        // Filter modal data based on search term
        if (searchTerm) {
            const filtered = modalData.filter(item => {
                if (typeof item === 'string') {
                    return item.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.TripTypeName) {
                    return item.TripTypeName.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.VehicleId) {
                    return item.VehicleId.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.DesigName) {
                    return item.DesigName.toLowerCase().includes(searchTerm.toLowerCase());
                } else if (item.EmpName) {
                    return item.EmpName.toLowerCase().includes(searchTerm.toLowerCase());
                }
                return false;
            });
            setFilteredModalData(filtered);
        } else {
            setFilteredModalData(modalData);
        }
    }, [searchTerm, modalData]);

    // Handle select all checkbox for operators
    useEffect(() => {
        if (isSelectAllOperators) {
            const allIndices = operatorsList.map((_, index) => index);
            setSelectedRows(allIndices);
        } else {
            setSelectedRows([]);
        }
    }, [isSelectAllOperators]);

    // Monitor API data changes and update modal if it's open
    useEffect(() => {
        console.log('🔄 tripTypeOptions changed:', tripTypeOptions.length, 'items');
        if (modalVisible && modalType === 'tripType' && tripTypeOptions.length > 0) {
            console.log('🔄 Updating modal with new trip type data');
            setModalData(tripTypeOptions);
            setFilteredModalData(tripTypeOptions);
        }
    }, [tripTypeOptions, modalVisible, modalType]);

    useEffect(() => {
        console.log('🔄 operatorTypeOptions changed:', operatorTypeOptions.length, 'items');
        if (modalVisible && modalType === 'operatorType' && operatorTypeOptions.length > 0) {
            console.log('🔄 Updating modal with new operator type data');
            setModalData(operatorTypeOptions);
            setFilteredModalData(operatorTypeOptions);
        }
    }, [operatorTypeOptions, modalVisible, modalType]);

    useEffect(() => {
        console.log('🔄 vehicleOptions changed:', vehicleOptions.length, 'items');
        if (modalVisible && modalType === 'vehicleNo' && vehicleOptions.length > 0) {
            console.log('🔄 Updating modal with new vehicle data');
            setModalData(vehicleOptions);
            setFilteredModalData(vehicleOptions);
        }
    }, [vehicleOptions, modalVisible, modalType]);

    useEffect(() => {
        console.log('🔄 operatorOptions changed:', operatorOptions.length, 'items');
        if (modalVisible && modalType === 'operator' && operatorOptions.length > 0) {
            console.log('🔄 Updating modal with new operator data');
            setModalData(operatorOptions);
            setFilteredModalData(operatorOptions);
        }
    }, [operatorOptions, modalVisible, modalType]);

    const getAuthToken = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');

            console.log('🔑 Retrieved from AsyncStorage:');
            console.log('🔑 Auth Token:', token ? 'Present' : 'Missing');
            console.log('🔑 User Data:', userData ? 'Present' : 'Missing');
            console.log('🔑 Selected Branch:', selectedBranch ? 'Present' : 'Missing');

            if (token) {
                setAuthToken(token);
                console.log('✅ Auth token loaded successfully');
            } else {
                console.warn('⚠️ No auth token found in storage');
                Alert.alert('Authentication Error', 'Please login again to continue');
            }

            // Get current branch from selectedBranch (this is the main source now)
            if (selectedBranch) {
                const branch = JSON.parse(selectedBranch);
                console.log('🏢 Selected Branch object:', branch);
                setCurrentBranch(branch);

                // Extract branch ID - try multiple possible property names
                let branchId = null;
                if (branch.id) {
                    branchId = branch.id;
                    console.log('✅ Using branch.id:', branchId);
                } else if (branch.branchId) {
                    branchId = branch.branchId;
                    console.log('✅ Using branch.branchId:', branchId);
                } else if (branch.BranchID) {
                    branchId = branch.BranchID;
                    console.log('✅ Using branch.BranchID:', branchId);
                } else if (branch.BranchId) {
                    branchId = branch.BranchId;
                    console.log('✅ Using branch.BranchId:', branchId);
                } else if (branch.branchCode) {
                    branchId = branch.branchCode;
                    console.log('✅ Using branch.branchCode:', branchId);
                } else if (branch.code) {
                    branchId = branch.code;
                    console.log('✅ Using branch.code:', branchId);
                }

                if (branchId) {
                    setCurrentBranchId(branchId);
                    console.log('✅ Current branch ID set to:', branchId);
                } else {
                    console.error('❌ Could not find branch ID in branch object');
                    console.error('❌ Branch object keys:', Object.keys(branch));
                }

                // Also try to get branch name for display
                let branchName = branch.name || branch.branchName || branch.BranchName || branch.displayName || 'Selected Branch';
                console.log('✅ Branch name:', branchName);
            } else {
                console.warn('⚠️ No selected branch found in storage');
                Alert.alert('Branch Required', 'Please select a branch to continue');
            }

            // Fallback: If you need user data for other purposes
            if (userData) {
                const user = JSON.parse(userData);
                console.log('👤 User data loaded:', user);
            }
        } catch (error) {
            console.error('❌ Error getting stored data:', error);
            Alert.alert('Error', 'Failed to retrieve stored data');
        }
    };

    const makeApiCall = async (url, options = {}) => {
        console.log('🔵 API Call Started:', url);
        console.log('🔵 Auth Token:', authToken ? `${authToken.substring(0, 50)}...` : 'Missing');
        console.log('🔵 Full Auth Token:', authToken);
        console.log('🔵 Current Branch ID:', currentBranchId);

        if (!authToken) {
            console.error('❌ No auth token available');
            Alert.alert('Authentication Error', 'Please login again to continue');
            return null;
        }

        try {
            console.log('🔵 Making API request to:', url);
            console.log('🔵 Authorization header:', `Bearer ${authToken}`);
            console.log('🔵 Request headers:', {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
                ...options.headers,
            });

            const response = await fetch(url, {
                ...options,
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
            });

            console.log('🔵 Response status:', response.status);
            console.log('🔵 Response statusText:', response.statusText);
            console.log('🔵 Response headers:', Object.fromEntries(response.headers.entries()));

            if (response.status === 401) {
                console.error('❌ Session expired (401) - Token may be invalid:', authToken);
                Alert.alert('Session Expired', 'Please login again to continue');
                return null;
            }

            if (!response.ok) {
                console.error('❌ HTTP error:', response.status, response.statusText);
                console.error('❌ Used token:', authToken);
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            console.log('✅ API Response data:', data);
            console.log('✅ API Call completed successfully for:', url);
            console.log('✅ Used token:', authToken);
            return data;
        } catch (error) {
            console.error('❌ API call error for:', url);
            console.error('❌ Used token:', authToken);
            console.error('❌ Error details:', error);
            console.error('❌ Error message:', error.message);
            console.error('❌ Error stack:', error.stack);
            Alert.alert('Error', `Failed to fetch data: ${error.message}`);
            return null;
        }
    };

    const loadTripTypes = async () => {
        console.log('🟡 Loading Trip Types...');
        console.log('🟡 Trip Types - Auth Token:', authToken);
        console.log('🟡 Trip Types - Current Branch:', currentBranchId);
        console.log('🟡 Current tripTypeOptions before API call:', tripTypeOptions);
        setLoading(true);
        try {
            const data = await makeApiCall(`${local_signage}/api/v1/triptype`);
            if (data) {
                console.log('✅ Trip Types loaded:', data.length, 'items');
                console.log('✅ Trip Types data:', data);
                console.log('✅ Setting tripTypeOptions state...');
                setTripTypeOptions(data);
                console.log('✅ tripTypeOptions state set');

                // If modal is open for trip types, update it immediately
                if (modalVisible && modalType === 'tripType') {
                    console.log('✅ Modal is open for trip types, updating modal data immediately');
                    setModalData(data);
                    setFilteredModalData(data);
                }
            } else {
                console.warn('⚠️ No trip types data received');
            }
        } catch (error) {
            console.error('❌ Error loading trip types:', error);
        } finally {
            setLoading(false);
            console.log('🟡 Trip Types loading completed');
        }
    };

    const loadVehicleNumbers = async (branchId) => {
        if (!branchId) {
            console.warn('⚠️ No branch ID provided for vehicle numbers');
            return;
        }

        console.log('🟡 Loading Vehicle Numbers for branch:', branchId);
        console.log('🟡 Vehicle Numbers - Auth Token:', authToken);
        console.log('🟡 Vehicle Numbers - Current Branch:', currentBranchId);
        setLoading(true);
        try {
            const url = `${local_signage}/api/v1/vehicleno?BranchID=${branchId}`;
            console.log('🟡 Vehicle API URL:', url);
            console.log('🟡 Vehicle API - Using Auth Token:', authToken);

            const data = await makeApiCall(url);
            if (data) {
                console.log('✅ Vehicle Numbers loaded:', data.length, 'items');
                console.log('✅ Vehicle Numbers data:', data);
                setVehicleOptions(data);
                // Update modal data if modal is open
                if (modalType === 'vehicleNo') {
                    console.log('✅ Updating modal data with vehicle numbers');
                    setModalData(data);
                    setFilteredModalData(data);
                }
            } else {
                console.warn('⚠️ No vehicle numbers data received');
            }
        } catch (error) {
            console.error('❌ Error loading vehicle numbers:', error);
        } finally {
            setLoading(false);
            console.log('🟡 Vehicle Numbers loading completed');
        }
    };

    const loadOperatorTypes = async () => {
        console.log('🟡 Loading Operator Types...');
        console.log('🟡 Operator Types - Auth Token:', authToken);
        console.log('🟡 Operator Types - Current Branch:', currentBranchId);
        console.log('🟡 Current operatorTypeOptions before API call:', operatorTypeOptions);
        setLoading(true);
        try {
            const data = await makeApiCall(`${local_signage}/api/v1/operatortype`);
            if (data) {
                console.log('✅ Operator Types loaded:', data.length, 'items');
                console.log('✅ Operator Types data:', data);
                console.log('✅ Setting operatorTypeOptions state...');
                setOperatorTypeOptions(data);
                console.log('✅ operatorTypeOptions state set');

                // If modal is open for operator types, update it immediately
                if (modalVisible && modalType === 'operatorType') {
                    console.log('✅ Modal is open for operator types, updating modal data immediately');
                    setModalData(data);
                    setFilteredModalData(data);
                }
            } else {
                console.warn('⚠️ No operator types data received');
            }
        } catch (error) {
            console.error('❌ Error loading operator types:', error);
        } finally {
            setLoading(false);
            console.log('🟡 Operator Types loading completed');
        }
    };

    const loadOperators = async (designTypeCode) => {
        if (!designTypeCode) {
            console.warn('⚠️ No design type code provided for operators');
            return;
        }

        console.log('🟡 Loading Operators for design type:', designTypeCode);
        console.log('🟡 Operators - Auth Token:', authToken);
        console.log('🟡 Operators - Current Branch ID:', currentBranchId);
        setLoading(true);
        try {
            const url = `${local_signage}/api/v1/operator?CurrentBranchId=L105&DesigTypeCode=${designTypeCode}`;
            console.log('🟡 Operator API URL:', url);
            console.log('🟡 Operator API - Using Auth Token:', authToken);

            const data = await makeApiCall(url);
            if (data) {
                console.log('✅ Operators loaded:', data.length, 'items');
                console.log('✅ Operators data:', data);
                setOperatorOptions(data);
                // Update modal data if modal is open
                if (modalType === 'operator') {
                    console.log('✅ Updating modal data with operators');
                    setModalData(data);
                    setFilteredModalData(data);
                }
            } else {
                console.warn('⚠️ No operators data received');
            }
        } catch (error) {
            console.error('❌ Error loading operators:', error);
        } finally {
            setLoading(false);
            console.log('🟡 Operators loading completed');
        }
    };

    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            saveTrip();
        } else if (option === 'View') {
            // Logic for view
        } else if (option === 'Cancel') {
            resetForm();
        }
    };

    const resetForm = () => {
        setSelectedTripType('');
        setSelectedTripTypeCode('');
        setIsOwnBranchVehicle(true);
        setVehicleBranch('');
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setDestination('');
        setRemarks('');
        setOperatorType('');
        setSelectedOperator('');
        setSelectedOperatorId('');
        setSelectedOperatorDesigCode(''); // ADD THIS LINE
        setSelectedRows([]);
        setOperatorsList([]);
    };

    const saveTrip = async () => {
        console.log('💾 Starting trip save process...');

        // Validation logic
        if (!selectedTripType) {
            Alert.alert('Validation Error', 'Please select trip type');
            return;
        }

        if (!vehicleBranch) {
            Alert.alert('Validation Error', 'Please select vehicle branch');
            return;
        }

        if (!vehicleNo) {
            Alert.alert('Validation Error', 'Please select vehicle number');
            return;
        }

        if (!destination) {
            Alert.alert('Validation Error', 'Please select destination');
            return;
        }

        if (operatorsList.length === 0) {
            Alert.alert('Validation Error', 'Please add at least one operator');
            return;
        }

        // Additional validation for required IDs
        if (!currentBranchId) {
            Alert.alert('Validation Error', 'Branch information is missing. Please login again.');
            return;
        }

        setLoading(true);

        try {
            // Get current user data for created/modified fields
            const userData = await AsyncStorage.getItem('userData');
            const user = userData ? JSON.parse(userData) : null;
            const currentUserId = user?.userId || user?.id || user?.empId || 'SYSTEM';

            console.log('💾 User data for save:', user);
            console.log('💾 Current user ID:', currentUserId);
            console.log('💾 Current branch ID:', currentBranchId);
            console.log('💾 Selected trip type:', selectedTripType, selectedTripTypeCode);
            console.log('💾 Vehicle info:', vehicleNo, vehicleBranch);
            console.log('💾 Destination:', destination);
            console.log('💾 Operators list:', operatorsList);

            // Get current date
            const currentDate = new Date().toISOString();

            // Prepare trip operators array
            const tripOperators = operatorsList.map(operator => ({
                lineNumber: operator.lineNumber.toString(),
                operatorDesigName: operator.operatorType,
                operatorName: operator.operatorName,
                operatorDesigCode: operator.operatorDesigCode, // CHANGE: Use actual DesigCode instead of getDesignTypeCode(operator.operatorType)
                operatorEmpID: operator.operatorId,
                deleted: "N",
                createdUserId: currentUserId,
                createdDate: currentDate,
                modifiedUserId: currentUserId,
                modifiedDate: currentDate,
                deletedUserId: "string",
                deletedDate: currentDate,
                dml: "I" // I for Insert, U for Update, D for Delete
            }));

            // Prepare the request body
            const tripData = {
                tripID: "",
                branchId: currentBranchId,
                tripTypeName: selectedTripType,
                tripDate: currentDate,
                vehicleNo: vehicleNo,
                destPlaceName: destination,
                destBranchName: destination,
                remarks: remarks || "",
                isO_Number: "",
                startReading: parseInt(startReading) || 0,
                endReading: parseInt(endReading) || 0,
                tripTypeCode: selectedTripTypeCode,
                destPlaceID: getBranchId(destination),
                destBranchID: getBranchId(destination),
                vehicleBranchId: getBranchId(vehicleBranch),
                closed: false,
                closedOnBusinessDate: currentDate,
                isFullDayTrip: true,
                deleted: "N",
                createdUserId: currentUserId,
                createdDate: currentDate,
                modifiedUserId: currentUserId,
                modifiedDate: currentDate,
                deletedUserId: "string",
                deletedDate: currentDate,
                tripStatusId: "1",
                timeSlotId: "",
                timeSlotName: "",
                paymentMethodId: "",
                paymentGatewayId: "",
                tpVehicleNo: vehicleNo,
                tripOperators: tripOperators
            };

            console.log('💾 Trip data to be sent:', JSON.stringify(tripData, null, 2));
            console.log('💾 Validating required fields...');
            console.log('💾 - tr field:', tripData.tr);
            console.log('💾 - branchId:', tripData.branchId);
            console.log('💾 - tripTypeName:', tripData.tripTypeName);
            console.log('💾 - vehicleNo:', tripData.vehicleNo);
            console.log('💾 - tripOperators length:', tripData.tripOperators.length);

            // Make the API call
            const response = await makeApiCall(`${local_url}/api/Trip2`, {
                method: 'POST',
                body: JSON.stringify(tripData),
            });

            if (response) {
                console.log('✅ Trip saved successfully:', response);
                Alert.alert(
                    'Success',
                    'Trip saved successfully!',
                    [
                        {
                            text: 'OK',
                            onPress: () => {
                                resetForm(); // Clear the form after successful save
                            }
                        }
                    ]
                );
            } else {
                console.error('❌ Failed to save trip - no response received');
                Alert.alert('Error', 'Failed to save trip. Please try again.');
            }
        } catch (error) {
            console.error('❌ Error saving trip:', error);
            Alert.alert('Error', `Failed to save trip: ${error.message}`);
        } finally {
            setLoading(false);
            console.log('💾 Trip save process completed');
        }
    };

    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
            setIsSelectAllOperators(false);
        } else {
            setSelectedRows([...selectedRows, index]);
            // Check if all rows are now selected
            if (selectedRows.length + 1 === operatorsList.length) {
                setIsSelectAllOperators(true);
            }
        }
    };

    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newOperators = operatorsList.filter((_, index) => !selectedRows.includes(index));
        setOperatorsList(newOperators);
        setSelectedRows([]);
        setIsSelectAllOperators(false);
    };

    const handleClearTripDetails = () => {
        setSelectedTripType('');
        setSelectedTripTypeCode('');
        setVehicleBranch('');
        setVehicleNo('');
        setStartReading('');
        setEndReading('');
        setDestination('');
        setRemarks('');
        // Optionally clear operator details too:
        setOperatorType('');
        setSelectedOperator('');
        setSelectedOperatorId('');
        setSelectedOperatorDesigCode(''); // ADD THIS LINE
    };


    const openModal = async (type) => {
        console.log('🟠 Opening modal for type:', type);
        console.log('🟠 Current state - tripTypeOptions length:', tripTypeOptions.length);
        console.log('🟠 Current state - tripTypeOptions data:', tripTypeOptions);
        console.log('🟠 Current state - operatorTypeOptions length:', operatorTypeOptions.length);
        console.log('🟠 Current state - vehicleOptions length:', vehicleOptions.length);

        setSearchTerm(''); // Reset search term
        setModalType(type);
        setModalVisible(true);

        switch (type) {
            case 'tripType':
                console.log('🟠 Setting trip type options:', tripTypeOptions.length, 'items');
                console.log('🟠 Trip type options data:', tripTypeOptions);

                // If tripTypeOptions is empty, reload the data
                if (tripTypeOptions.length === 0) {
                    console.log('🟠 Trip type options empty, reloading...');
                    setLoading(true);
                    setModalData([]);
                    setFilteredModalData([]);
                    await loadTripTypes();
                    console.log('🟠 After reload - tripTypeOptions:', tripTypeOptions.length);
                } else {
                    setModalData(tripTypeOptions);
                    setFilteredModalData(tripTypeOptions);
                }
                break;
            case 'vehicleBranch':
                const branchOptions = getBranchOptions();
                console.log('🟠 Setting branch options:', branchOptions);
                setModalData(branchOptions);
                setFilteredModalData(branchOptions);
                break;
            case 'vehicleNo':
                if (!vehicleBranch) {
                    console.warn('⚠️ No vehicle branch selected');
                    Alert.alert('Error', 'Please select vehicle branch first');
                    setModalVisible(false);
                    return;
                }
                // Clear previous data and show loading
                console.log('🟠 Clearing previous vehicle data and loading new...');
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                // Load vehicle numbers based on selected branch
                const branchId = getBranchId(vehicleBranch);
                console.log('🟠 Getting vehicles for branch:', vehicleBranch, 'with ID:', branchId);
                await loadVehicleNumbers(branchId);
                break;
            case 'destination':
                const destinationOptions = getBranchOptions();
                console.log('🟠 Setting destination options:', destinationOptions);
                setModalData(destinationOptions);
                setFilteredModalData(destinationOptions);
                break;
            case 'operatorType':
                console.log('🟠 Setting operator type options:', operatorTypeOptions.length, 'items');
                console.log('🟠 Operator type options data:', operatorTypeOptions);

                // If operatorTypeOptions is empty, reload the data
                if (operatorTypeOptions.length === 0) {
                    console.log('🟠 Operator type options empty, reloading...');
                    setLoading(true);
                    setModalData([]);
                    setFilteredModalData([]);
                    await loadOperatorTypes();
                    console.log('🟠 After reload - operatorTypeOptions:', operatorTypeOptions.length);
                } else {
                    setModalData(operatorTypeOptions);
                    setFilteredModalData(operatorTypeOptions);
                }
                break;
            case 'operator':
                if (!operatorType) {
                    console.warn('⚠️ No operator type selected');
                    Alert.alert('Error', 'Please select operator type first');
                    setModalVisible(false);
                    return;
                }
                // Clear previous data and show loading
                console.log('🟠 Clearing previous operator data and loading new...');
                setLoading(true);
                setModalData([]);
                setFilteredModalData([]);
                // Load operators based on selected operator type
                const designTypeCode = getDesignTypeCode(operatorType);
                console.log('🟠 Getting operators for type:', operatorType, 'with code:', designTypeCode);
                await loadOperators(designTypeCode);
                break;
            default:
                console.log('🟠 Default case - clearing modal data');
                setModalData([]);
                setFilteredModalData([]);
        }
        console.log('🟠 Modal opened successfully for type:', type);
        console.log('🟠 Final modalData length:', modalData.length);
    };

    const getBranchId = (branchName) => {
        console.log('🟣 Getting branch ID for branch name:', branchName);
        console.log('🟣 Current branch object:', currentBranch);
        console.log('🟣 Current branch ID from state:', currentBranchId);

        // Primary: Use the currentBranchId from selectedBranch
        if (currentBranchId) {
            console.log('✅ Using current branch ID from state:', currentBranchId);
            return currentBranchId;
        }

        // Secondary: Try to extract from currentBranch object
        if (currentBranch) {
            if (currentBranch.id) {
                console.log('✅ Using branch.id:', currentBranch.id);
                return currentBranch.id;
            } else if (currentBranch.branchId) {
                console.log('✅ Using branch.branchId:', currentBranch.branchId);
                return currentBranch.branchId;
            } else if (currentBranch.BranchID) {
                console.log('✅ Using branch.BranchID:', currentBranch.BranchID);
                return currentBranch.BranchID;
            } else if (currentBranch.BranchId) {
                console.log('✅ Using branch.BranchId:', currentBranch.BranchId);
                return currentBranch.BranchId;
            } else if (currentBranch.branchCode) {
                console.log('✅ Using branch.branchCode:', currentBranch.branchCode);
                return currentBranch.branchCode;
            } else if (currentBranch.code) {
                console.log('✅ Using branch.code:', currentBranch.code);
                return currentBranch.code;
            }
        }

        // Fallback: Map branch names to IDs (only if no selectedBranch available)
        console.warn('⚠️ No branch ID found in selectedBranch, using fallback mapping');
        const branchMap = {
            'Ajni': 'R607',
            'Abis': 'R117',
            'Laziz Shop': 'R118'
        };
        const mappedId = branchMap[branchName] || 'R607';
        console.log('⚠️ Using fallback branch ID:', mappedId);
        return mappedId;
    };

    const getDesignTypeCode = (operatorTypeName) => {
        console.log('🟣 Getting design type code for:', operatorTypeName);

        // Map operator type names to codes
        const typeMap = {
            'PILOT': 'VEHST',
            'CO PILOT': 'VEHST'
        };
        const code = typeMap[operatorTypeName] || 'VEHST';
        console.log('✅ Design type code:', code);
        return code;
    };

    const getLoadingMessage = (type) => {
        if (type === 'saving') {
            return 'Saving trip...';
        }

        switch (type) {
            case 'tripType':
                return 'Loading trip types...';
            case 'vehicleNo':
                return 'Loading vehicles...';
            case 'operatorType':
                return 'Loading operator types...';
            case 'operator':
                return 'Loading operators...';
            default:
                return 'Loading data...';
        }
    };

    const handleModalItemSelect = (item) => {
        console.log('🟢 Modal item selected:', item);
        console.log('🟢 Modal type:', modalType);

        switch (modalType) {
            case 'tripType':
                console.log('🟢 Setting trip type:', item.TripTypeName, 'with code:', item.TripTypeCode);
                setSelectedTripType(item.TripTypeName);
                setSelectedTripTypeCode(item.TripTypeCode);
                break;
            case 'vehicleBranch':
                console.log('🟢 Setting vehicle branch:', item);
                setVehicleBranch(item);
                // Reset vehicle number when branch changes
                setVehicleNo('');
                console.log('🟢 Vehicle number reset due to branch change');
                break;
            case 'vehicleNo':
                console.log('🟢 Setting vehicle number:', item.VehicleId);
                setVehicleNo(item.VehicleId);
                break;
            case 'destination':
                console.log('🟢 Setting destination:', item);
                setDestination(item);
                break;
            case 'operatorType':
                console.log('🟢 Setting operator type:', item.DesigName);
                setOperatorType(item.DesigName);
                // Reset selected operator when type changes
                setSelectedOperator('');
                setSelectedOperatorId('');
                console.log('🟢 Operator selection reset due to type change');
                break;
            case 'operator':
                console.log('🟢 Setting operator:', item.EmpName, 'with ID:', item.EmpId, 'and DesigCode:', item.DesigCode);
                setSelectedOperator(item.EmpName);
                setSelectedOperatorId(item.EmpId);
                setSelectedOperatorDesigCode(item.DesigCode); // ADD THIS LINE
                break;
        }
        setModalVisible(false);
        console.log('🟢 Modal closed after selection');
    };

    // New function to add operator to the list
    const addOperator = () => {
        if (!operatorType || !selectedOperator) {
            Alert.alert('Validation Error', 'Please select both Operator Type and Operator');
            return;
        }

        // Create a new operator entry
        const newOperator = {
            lineNumber: operatorsList.length + 1,
            operatorType: operatorType,
            operatorName: selectedOperator,
            operatorId: selectedOperatorId,
            operatorDesigCode: selectedOperatorDesigCode // ADD THIS LINE
        };

        // Add to operators list
        setOperatorsList([...operatorsList, newOperator]);

        // Reset operator fields for next entry
        setOperatorType('');
        setSelectedOperator('');
        setSelectedOperatorId('');
        setSelectedOperatorDesigCode(''); // ADD THIS LINE
    };

    const renderModalItem = (item, index) => {
        let displayText = '';

        if (modalType === 'operator' && item.EmpName) {
            displayText = item.EmpName;
        } else if (typeof item === 'string') {
            displayText = item;
        } else if (item.TripTypeName) {
            displayText = item.TripTypeName;
        } else if (item.VehicleId) {
            displayText = item.VehicleId;
        } else if (item.DesigName) {
            displayText = item.DesigName;
        } else if (item.EmpName) {
            displayText = item.EmpName;
        }

        return (
            <TouchableOpacity
                key={index}
                style={styles.modalItem}
                onPress={() => handleModalItemSelect(item)}
            >
                <Text style={styles.modalItemText}>{displayText}</Text>
            </TouchableOpacity>
        );
    };

    return (
        <View style={styles.container}>
            <Navbar />

            <ScrollOptionsBar
                title="Trips"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />

            <ScrollView style={styles.contentContainer}>
                {/* Trip Details Section */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Details</Text>

                    <View style={styles.formRow}>
                        <TouchableOpacity
                            style={styles.dropdown}
                            onPress={() => openModal('tripType')}
                        >
                            <Text style={[styles.inputText, !selectedTripType && styles.placeholderText]}>
                                {selectedTripType || 'Select Trip Type'}
                            </Text>
                            <Text style={styles.dropdownIcon}>▼</Text>
                        </TouchableOpacity>

                        <View style={styles.checkboxContainer}>
                            <CheckBox
                                value={!!isOwnBranchVehicle}
                                onValueChange={setIsOwnBranchVehicle}
                                tintColors={{ true: '#02096A', false: '#999' }}
                                style={styles.checkbox}
                            />
                            <Text style={styles.checkboxLabel}>Own Branch Vehicle</Text>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Vehicle Branch"
                                placeholderTextColor="#888"
                                value={vehicleBranch}
                                onChangeText={setVehicleBranch}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('vehicleBranch')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>

                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Vehicle No."
                                placeholderTextColor="#888"
                                value={vehicleNo}
                                onChangeText={setVehicleNo}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('vehicleNo')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.readingInput}
                            placeholder="Start Reading"
                            placeholderTextColor="#888"
                            value={startReading}
                            onChangeText={setStartReading}
                            keyboardType="numeric"
                        />

                        <TextInput
                            style={styles.readingInput}
                            placeholder="End Reading"
                            placeholderTextColor="#888"
                            value={endReading}
                            onChangeText={setEndReading}
                            keyboardType="numeric"
                        />
                    </View>

                    <View style={styles.formRow}>
                        <View style={styles.inputWithButtonContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Destination"
                                placeholderTextColor="#888"
                                value={destination}
                                onChangeText={setDestination}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('destination')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    <View style={styles.formRow}>
                        <TextInput
                            style={styles.remarksInput}
                            placeholder="Add Remarks"
                            placeholderTextColor="#888"
                            multiline
                            numberOfLines={3}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                    </View>

                    <View style={styles.buttonRow}>
                        <TouchableOpacity style={styles.clearButton} onPress={handleClearTripDetails}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Operator Details Section */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Operator details</Text>

                    <View style={styles.operatorInputRow}>
                        <TouchableOpacity
                            style={styles.operatorTypeDropdown}
                            onPress={() => openModal('operatorType')}
                        >
                            <Text style={[styles.inputText, !operatorType && styles.placeholderText]}>
                                {operatorType || 'Operator Type'}
                            </Text>
                            <Text style={styles.dropdownIcon}>▼</Text>
                        </TouchableOpacity>

                        <View style={styles.operatorNameContainer}>
                            <TextInput
                                style={styles.inputWithButton}
                                placeholder="Select Operator"
                                placeholderTextColor="#888"
                                value={selectedOperator}
                                onChangeText={setSelectedOperator}
                                editable={false}
                            />
                            <TouchableOpacity
                                style={styles.selectButton}
                                onPress={() => openModal('operator')}
                            >
                                <Text style={styles.selectButtonText}>Select</Text>
                            </TouchableOpacity>
                        </View>

                        {/* Add button inline with operator selection */}
                        <TouchableOpacity style={styles.addButton} onPress={addOperator}>
                            <Text style={styles.addButtonText}>Add</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Operators Table with Header always visible */}
                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <View style={styles.checkboxCell}>
                                <CheckBox
                                    value={!!isSelectAllOperators}
                                    onValueChange={setIsSelectAllOperators}
                                    tintColors={{ true: 'white', false: 'white' }}
                                    style={styles.headerCheckbox}
                                />
                            </View>
                            <View style={styles.lineNumberCell}>
                                <Text style={styles.tableHeaderText}>Line#</Text>
                            </View>
                            <View style={styles.operatorTypeCell}>
                                <Text style={styles.tableHeaderText}>Type</Text>
                            </View>
                            <View style={styles.operatorNameCell}>
                                <Text style={styles.tableHeaderText}>Operator</Text>
                            </View>
                        </View>

                        <ScrollView style={styles.tableBody} contentContainerStyle={{ flexGrow: 1 }}>
                            {operatorsList.length > 0 ? (
                                operatorsList.map((operator, index) => (
                                    <View key={index} style={styles.tableRow}>
                                        <View style={styles.checkboxCell}>
                                            <CheckBox
                                                value={selectedRows.includes(index)}
                                                onValueChange={() => handleRowSelection(index)}
                                                tintColors={{ true: '#02096A', false: '#999' }}
                                            />
                                        </View>
                                        <View style={styles.lineNumberCell}>
                                            <Text style={styles.tableCell}>{operator.lineNumber}</Text>
                                        </View>
                                        <View style={styles.operatorTypeCell}>
                                            <Text style={styles.tableCell}>{operator.operatorType}</Text>
                                        </View>
                                        <View style={styles.operatorNameCell}>
                                            <Text style={styles.tableCell}>{operator.operatorName}</Text>
                                        </View>
                                    </View>
                                ))
                            ) : (
                                <View style={styles.emptyTableRow}>
                                    <Text style={styles.emptyTableText}>No Data</Text>
                                </View>
                            )}
                        </ScrollView>
                    </View>

                    {operatorsList.length > 0 && (
                        <View style={styles.buttonRow}>
                            <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteSelectedRows}>
                                <Text style={styles.deleteButtonText}>Delete Selected Row</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            </ScrollView>

            {/* Global Loading Overlay for Save Operation */}
            {loading && !modalVisible && (
                <CustomLoader
                    isVisible={loading}
                    message="Saving trip..."
                    size="large"
                    color="#02096A"
                    backgroundColor="rgba(0, 0, 0, 0.5)"
                    containerStyle={styles.globalLoaderContainer}
                />
            )}

            {/* Selection Modal with Yellow Boxes and Search */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'tripType' ? 'Select Trip Type' :
                                modalType === 'vehicleBranch' ? 'Select Vehicle Branch' :
                                    modalType === 'vehicleNo' ? 'Select Vehicle No.' :
                                        modalType === 'destination' ? 'Select Destination' :
                                            modalType === 'operatorType' ? 'Select Operator Type' :
                                                modalType === 'operator' ? 'Select Operator' : 'Select Item'}
                        </Text>

                        {/* Search input */}
                        <View style={styles.searchContainer}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search..."
                                placeholderTextColor="#888"
                                value={searchTerm}
                                onChangeText={setSearchTerm}
                            />
                        </View>

                        {/* Custom Loader Component - shows only when loading */}
                        <CustomLoader
                            isVisible={loading}
                            message={getLoadingMessage(modalType)}
                            size="large"
                            color="#02096A"
                            backgroundColor="rgba(255, 255, 255, 0.95)"
                        />

                        {/* Content - shows only when not loading */}
                        {!loading && (
                            <ScrollView>
                                <View style={styles.modalItemsContainer}>
                                    {filteredModalData.length > 0 ? (
                                        filteredModalData.map((item, index) => renderModalItem(item, index))
                                    ) : (
                                        <View style={styles.noDataContainer}>
                                            <Text style={styles.noDataText}>No data available</Text>
                                        </View>
                                    )}
                                </View>
                            </ScrollView>
                        )}

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    contentContainer: {
        flex: 1,
    
    },
    sectionContainer: {
        backgroundColor: '#EBEBEB',
        borderRadius: 10,
        padding: 15,
        marginHorizontal: 8,
        marginVertical: 4,
    },
    sectionTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
        marginBottom: 15,
        color: '#02096A',
    },
    formRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 15,
    },
    operatorInputRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    dropdown: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 50,
        marginRight: 10,
    },
    operatorTypeDropdown: {
        width: '25%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 55,
        marginRight: 10,
    },
    operatorNameContainer: {
        flex: 1,
        flexDirection: 'row',
        marginRight: 10,
    },
    inputText: {
        color: '#333',
        fontFamily: 'Poppins',
        fontSize: 15,
        fontWeight: 'bold',
    },
    placeholderText: {
        color: '#888',
    },
    dropdownIcon: {
        color: '#333',
        fontSize: 16,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 0.6,
        height: 50,
    },
    checkbox: {
        transform: [{ scale: 1.2 }],
    },
    headerCheckbox: {
        transform: [{ scale: 1.2 }],
    },
    checkboxLabel: {
        marginLeft: 8,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    inputWithButtonContainer: {
        flex: 1,
        flexDirection: 'row',
        marginRight: 10,
        marginBottom: 5,
    },
    fullWidthInputWithButtonContainer: {
        flex: 1,
        flexDirection: 'row',
    },
    inputWithButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        height: 50,
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        fontWeight: '700',
    },
    selectButton: {
        backgroundColor: '#041C44',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
    },
    selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    readingInput: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        height: 50,
        fontFamily: 'Poppins',
        fontSize: 15,
        color: '#333',
        borderWidth: 1,
        borderColor: '#ccc',
        marginRight: 10,fontWeight: 'bold',
    },
    readingButton: {
        flex: 1,
        backgroundColor: '#d8d8d8',
        justifyContent: 'center',
        alignItems: 'center',
        height: 50,
        borderRadius: 8,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    readingButtonText: {
        fontSize: 15,
        color: '#333',
        fontFamily: 'Poppins',
    },
    remarksInput: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        height: 80,
        textAlignVertical: 'top',
        fontFamily: 'Poppins',
        fontSize: 14,
        color: '#333',
        borderWidth: 1,
        borderColor: '#ccc',
        fontWeight: 'bold',
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: 15,
    },
    clearButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 40,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    clearButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    addButton: {
        backgroundColor: '#02720F',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
        height: 50,
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    deleteButton: {
        backgroundColor: '#FF0000',
        paddingVertical: 10,
        paddingHorizontal: 30,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    deleteButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    tableContainer: {
        backgroundColor: 'white',
        borderRadius: 8,
        overflow: 'hidden',
        marginBottom: 15,
        marginTop: 15,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#041C44',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 10,
        height: 45,
    },
    tableHeaderText: {
        fontWeight: 'bold',
        fontSize: 14,
        color: 'white',
        fontFamily: 'Poppins',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
        padding: 10,
        height: 45,
    },
    tableCell: {
        fontSize: 14,
        color: '#333',
        fontFamily: 'Poppins',
    },
    checkboxCell: {
        width: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    lineNumberCell: {
        width: 90,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    operatorTypeCell: {
        flex: 1,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    operatorNameCell: {
        flex: 2,
        paddingHorizontal: 5,
        justifyContent: 'center',
    },
    emptyTableRow: {
        padding: 15,
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#ddd',
    },
    emptyTableText: {
        color: '#999',
        fontFamily: 'Poppins',
        fontSize: 14,
    },

    // Modal styles with white background and yellow boxes
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
        textAlign: 'center',
        color: '#02096A',
    },
    searchContainer: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 10,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    noDataContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 200,
        width: '100%',
    },
    noDataText: {
        fontSize: 16,
        color: '#999',
        fontFamily: 'Poppins',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
    },
    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    globalLoaderContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    tableBody: {
        height: 200, // or any fixed height you want
    },
});

export default TripScreen;