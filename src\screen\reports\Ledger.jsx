import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Navbar from '../../components/Navbar';
import DateTimePicker from '@react-native-community/datetimepicker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import { fetchBranchDetails } from '../../apiHandling/ReportAPI/fetchBillReportsAPI';
// Comment out large customer APIs for now
// import { fetchHOCustomers, fetchSillakCustomers } from '../../apiHandling/ReportAPI/customerSearchAPI';

// Use BillingScreen customer search APIs instead
import { customerNameSearchAPI } from '../../apiHandling/BillingAPI/customerNameSearchAPI';
import { customerMobileSearchAPI } from '../../apiHandling/BillingAPI/customerMobileSearchAPI';
import { fetchCreditBalance, fetchCreditLedger, formatDateToYYYYMMDD } from '../../apiHandling/ReportAPI/fetchLedgerReportsAPI';

const LedgerScreen = () => {
  const navigation = useNavigation();

  // UI control states
  const [selectedScrollOption, setSelectedScrollOption] = useState('');

  // Report form states
  const [reportType, setReportType] = useState('');
  const [customerType, setCustomerType] = useState('');
  const [custId, setCustId] = useState('');
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);

  // Customer search modal states (using BillingScreen approach)
  const [customerDialogVisible, setCustomerDialogVisible] = useState(false);
  const [customerList, setCustomerList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // Initialize dates on component mount
  useEffect(() => {
    const initializeDates = async () => {
      try {
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        const businessDate = await fetchBusinessDay(bearerToken, loginBranchID); // format: DD-MM-YYYY
        const [day, month, year] = businessDate.split('-');
        const from = new Date(`${year}-${month}-${day}`);
        const to = new Date(from);
        to.setDate(to.getDate() + 1); // next day

        setFromDate(from);
        setToDate(to);
      } catch (error) {
        console.error('Error initializing dates:', error);
        // Set default dates if API fails
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        setFromDate(today);
        setToDate(tomorrow);
      }
    };

    initializeDates();
  }, []);

  // Customer search function (from BillingScreen)
  const fetchCustomers = async (searchInput) => {
    setLoading(true);
    setCustomerList([]);
    let result;
    const bearerToken = await AsyncStorage.getItem('authToken');
    const selectedBranch = await AsyncStorage.getItem('selectedBranch');
    const parsedBranch = JSON.parse(selectedBranch);
    const loginBranchID = parsedBranch.BranchId;

    // Check if input is mobile number (10 digits) or name
    if (searchInput.length === 10 && /^\d+$/.test(searchInput)) {
      result = await customerMobileSearchAPI(loginBranchID, searchInput, bearerToken);
    } else {
      result = await customerNameSearchAPI(loginBranchID, searchInput,'CS', bearerToken);
    }

    if (result.success) {
      setCustomerList(result.customers);
      setCustomerDialogVisible(true);
    } else {
      Alert.alert("Error", result.message);
    }

    setLoading(false);
  };

  // Handle search button (simplified approach)
  const handleSearch = () => {
    // Check if any search criteria is provided
    if (!custId && !phone && !name) {
      Alert.alert('Error', 'Please enter Customer ID, Phone, or Name to search');
      return;
    }

    // Determine search input (prioritize mobile, then name, then ID)
    let searchInput = '';
    if (phone && phone.length === 10) {
      searchInput = phone;
    } else if (name && name.length >= 2) {
      searchInput = name;
    } else if (custId) {
      searchInput = custId;
    } else {
      Alert.alert('Error', 'Please enter at least 2 characters for name or 10 digits for mobile');
      return;
    }

    fetchCustomers(searchInput);
  };

  // Handle customer selection from modal (from BillingScreen)
  const handleCustomerSelect = (customer) => {
    setCustomerDialogVisible(false);
    setCustId(customer.customerCode || customer.custID || '');
    setName(customer.customerName || '');
    setPhone(customer.mobile || '');
    setSelectedCustomer(customer);
  };

  // Handle generate report
  const handleGenerateReport = async () => {
    if (!reportType) {
      Alert.alert('Error', 'Please select a report type');
      return;
    }
    if (!customerType) {
      Alert.alert('Error', 'Please select a customer type');
      return;
    }
    if (!selectedCustomer || !custId) {
      Alert.alert('Error', 'Please search and select a customer');
      return;
    }
    if (!fromDate || !toDate) {
      Alert.alert('Error', 'Please select both from and to dates');
      return;
    }

    try {
      const bearerToken = await AsyncStorage.getItem('authToken');
      const selectedBranch = await AsyncStorage.getItem('selectedBranch');
      const parsedBranch = JSON.parse(selectedBranch);
      const loginBranchID = parsedBranch.BranchId;

      if (reportType === 'credit-balance') {
        // Fetch credit balance data
        const creditBalanceData = await fetchCreditBalance(loginBranchID, custId, bearerToken);

        if (creditBalanceData.length === 0) {
          Alert.alert('No Data Found', 'No credit balance data found for the selected customer.');
        } else {
          // Navigate to credit balance report page
          navigation.navigate('CreditBalanceReportPage', {
            creditBalanceData,
            customerInfo: selectedCustomer,
            fromDate,
            toDate,
          });
        }
      } else if (reportType === 'credit-ledger') {
        // Format dates to YYYYMMDD
        const fromStr = formatDateToYYYYMMDD(fromDate);
        const toStr = formatDateToYYYYMMDD(toDate);

        // Fetch credit ledger data
        const creditLedgerData = await fetchCreditLedger(loginBranchID, custId, fromStr, toStr, bearerToken);

        if (creditLedgerData.length === 0) {
          Alert.alert('No Data Found', 'No credit ledger data found for the selected customer and date range.');
        } else {
          // Navigate to credit ledger report page
          navigation.navigate('CreditLedgerReportPage', {
            creditLedgerData,
            customerInfo: selectedCustomer,
            fromDate,
            toDate,
          });
        }
      }
    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert('Error', `Something went wrong: ${error.message}`);
    }
  };



  return (
    <View style={styles.container}>
      <Navbar />

      {/* Scroll Options */}
      <View style={styles.scrollOptions_container}>
        <View style={styles.scrollOptions_row}>
          {/* Page Title */}
          <TouchableOpacity style={styles.scrollOptions_backContainer}>
            <Text style={styles.scrollOptions_screenTitle}>Ledger</Text>
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.scrollOptions_buttonsContainer}>
            {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
              const isSelected = selectedScrollOption === option;
              let buttonStyle = [styles.scrollOptions_button];

              if (option === 'Cancel') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                });
              } else if (option === 'Save') {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02720F' : '#02A515',
                });
              } else {
                buttonStyle.push({
                  backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                });
              }

              return (
                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                  <TouchableOpacity
                    style={buttonStyle}
                    onPress={() => setSelectedScrollOption(option)}
                  >
                    <Text style={[
                      styles.scrollOptions_buttonText,
                      { color: isSelected ? 'white' : 'black' },
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </View>
      </View>

      {/* Main Content ScrollView */}
      <ScrollView style={{ flex: 1 }}>
        <View style={styles.reportContainer}>

          {/* Row 1: Report Type */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Report Type:</Text>
              <Dropdown
                data={[
                  { label: 'Credit Balance', value: 'credit-balance' },
                  { label: 'Credit Ledger', value: 'credit-ledger' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Report Type"
                value={reportType}
                onChange={(item) => setReportType(item.value)}
                style={styles.dropdown}
              />
            </View>
          </View>

          {/* Row 2: Customer Type */}
          <View style={styles.inputRow}>
            <View style={styles.dropdownContainer}>
              <Text style={styles.inputLabel}>Customer Type:</Text>
              <Dropdown
                data={[
                  { label: 'Sillak', value: 'sillak' },
                  { label: 'All Sillak Customer', value: 'all-sillak-customer' },
                  { label: 'HO Customer', value: 'ho-customer' },
                  { label: 'All HO Customer', value: 'all-ho-customer' },
                ]}
                labelField="label"
                valueField="value"
                placeholder="Select Customer Type"
                value={customerType}
                onChange={(item) => setCustomerType(item.value)}
                style={styles.dropdown}
              />
            </View>
          </View>

          {/* Row 3: Customer Search Fields */}
          <View style={styles.inputRow}>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Customer ID:</Text>
              <TextInput
                style={styles.textField}
                placeholder="Enter Customer ID"
                value={custId}
                onChangeText={setCustId}
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone:</Text>
              <TextInput
                style={styles.textField}
                placeholder="Enter Phone Number"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Name:</Text>
              <TextInput
                style={styles.textField}
                placeholder="Enter Customer Name"
                value={name}
                onChangeText={setName}
              />
            </View>
            <TouchableOpacity
              style={[styles.searchButton, loading && styles.disabledButton]}
              onPress={handleSearch}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.searchButtonText}>Search</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Row 4: Date Pickers */}
          <View style={styles.inputRow}>
            <View style={styles.dateContainer}>
              <Text style={styles.inputLabel}>From Date:</Text>
              <TouchableOpacity
                onPress={() => setShowFromDatePicker(true)}
                style={styles.datePickerButton}
              >
                <Text style={styles.dateText}>
                  {fromDate ? fromDate.toLocaleDateString() : 'Select From Date'}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.dateContainer}>
              <Text style={styles.inputLabel}>To Date:</Text>
              <TouchableOpacity
                onPress={() => setShowToDatePicker(true)}
                style={styles.datePickerButton}
              >
                <Text style={styles.dateText}>
                  {toDate ? toDate.toLocaleDateString() : 'Select To Date'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* From Date Picker */}
          {showFromDatePicker && (
            <DateTimePicker
              value={fromDate || new Date()}
              mode="date"
              display="default"
              onChange={(_, date) => {
                setShowFromDatePicker(false);
                if (date) {
                  setFromDate(date);
                  // Only auto-set toDate if user hasn't picked one yet
                  if (!toDate) {
                    const nextDay = new Date(date);
                    nextDay.setDate(nextDay.getDate() + 1);
                    setToDate(nextDay);
                  }
                }
              }}
            />
          )}

          {/* To Date Picker */}
          {showToDatePicker && (
            <DateTimePicker
              value={toDate || new Date()}
              mode="date"
              display="default"
              onChange={(_, date) => {
                setShowToDatePicker(false);
                if (date) {
                  if (fromDate && date.toDateString() === fromDate.toDateString()) {
                    Alert.alert("Date Error", "From Date and To Date cannot be the same.");
                  } else {
                    setToDate(date);
                  }
                }
              }}
            />
          )}

          {/* Row 5: Generate Report Button */}
          <View style={styles.generateButtonContainer}>
            <TouchableOpacity
              style={styles.generateButton}
              onPress={handleGenerateReport}
            >
              <Text style={styles.generateButtonText}>Generate Report</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Customer Search Modal (BillingScreen style) */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={customerDialogVisible}
        onRequestClose={() => setCustomerDialogVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Select Customer</Text>

            {/* Customer list */}
            <ScrollView style={styles.modalScrollContainer}>
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#02096A" />
                  <Text style={styles.loadingText}>Searching customers...</Text>
                </View>
              ) : (
                <View style={styles.modalItemsContainer}>
                  {customerList.length === 0 ? (
                    <Text style={styles.noDataText}>
                      No customers found.
                    </Text>
                  ) : (
                    customerList.map((customer, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.modalItem}
                        onPress={() => handleCustomerSelect(customer)}
                      >
                        <View style={styles.customerItem}>
                          <Text style={styles.customerName}>
                            {customer.customerName}
                          </Text>
                          <Text style={styles.customerDetails}>
                            ID: {customer.customerCode || customer.custID || 'N/A'}
                            {customer.mobile && ` | Mobile: ${customer.mobile}`}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))
                  )}
                </View>
              )}
            </ScrollView>

            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setCustomerDialogVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },

  // Scroll Options Styles
  scrollOptions_container: {
    backgroundColor: '#E6E6E6',
    paddingVertical: 8,
    marginTop: 0,
  },
  scrollOptions_row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  scrollOptions_backContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollOptions_screenTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'black',
  },
  scrollOptions_buttonsContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'flex-end',
  },
  scrollOptions_buttonWrapper: {
    width: '22%',
    marginHorizontal: 5,
  },
  scrollOptions_button: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  scrollOptions_buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  // Report Container Styles
  reportContainer: {
    backgroundColor: '#FFFFFF',
    padding: 15,
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Form Styles
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    gap: 15,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  dropdownContainer: {
    flex: 1,
  },
  dropdown: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#02096A',
  },
  inputContainer: {
    flex: 1,
  },
  textField: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#02096A',
  },
  searchButton: {
    height: 50,
    backgroundColor: '#02096A',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 23, // To align with text fields that have label
  },
  searchButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },

  // Date Picker Styles
  dateContainer: {
    flex: 1,
  },
  datePickerButton: {
    height: 50,
    backgroundColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: '#02096A',
    justifyContent: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
  },

  // Generate Button Styles
  generateButtonContainer: {
    alignItems: 'center',
    marginTop: 30,
  },
  generateButton: {
    backgroundColor: '#02096A',
    paddingHorizontal: 40,
    paddingVertical: 15,
    borderRadius: 10,
    minWidth: 200,
  },
  generateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#02096A',
    textAlign: 'center',
    marginBottom: 15,
  },
  searchContainer: {
    marginBottom: 15,
  },
  searchInput: {
    height: 45,
    borderWidth: 1,
    borderColor: '#02096A',
    borderRadius: 10,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: 'white',
  },
  modalScrollContainer: {
    maxHeight: 300,
    marginBottom: 15,
  },
  modalItemsContainer: {
    paddingVertical: 5,
  },
  modalItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    padding: 15,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  customerItem: {
    flex: 1,
  },
  customerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#02096A',
    marginBottom: 5,
  },
  customerDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  customerBranch: {
    fontSize: 12,
    color: '#888',
    fontStyle: 'italic',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  noDataText: {
    fontSize: 16,
    color: '#999',
    fontStyle: 'italic',
  },
  modalCloseButton: {
    backgroundColor: '#FF3333',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 30,
    alignSelf: 'center',
  },
  modalCloseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },

});

export default LedgerScreen;