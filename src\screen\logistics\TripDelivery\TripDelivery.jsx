import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    TextInput,
    Alert,
    Modal,
    FlatList,
    ActivityIndicator,
} from 'react-native';
import CheckBox from '@react-native-community/checkbox';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DatePicker from 'react-native-ui-datepicker';
import Navbar from '../../../components/Navbar';
import ScrollOptionsBar from '../../../components/ScrollOptionsBar';

export const TripDelivery = () => {
    // Trip Details states
    const [selectedVehicleBranch, setSelectedVehicleBranch] = useState('');
    const [tripId, setTripId] = useState('');
    const [tripDetails, setTripDetails] = useState('');
    const [tripStart, setTripStart] = useState('');
    const [tripEnd, setTripEnd] = useState('');
    const [selectedDocType, setSelectedDocType] = useState('');
    const [selectedDocTypeId, setSelectedDocTypeId] = useState(''); // Store TransTypeId
    const [selectedDocNo, setSelectedDocNo] = useState(''); // New state for selected doc number
    const [selectedDocData, setSelectedDocData] = useState(null); // Store complete doc data
    const [customerBranch, setCustomerBranch] = useState('');
    const [place, setPlace] = useState('');
    const [addRemarks, setAddRemarks] = useState('');
    const [weight, setWeight] = useState('');
    const [selectedScrollOption, setSelectedScrollOption] = useState('');

    // Table states
    const [expenseItems, setExpenseItems] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [modalType, setModalType] = useState('');
    const [modalData, setModalData] = useState([]);

    // New states for API and calendar
    const [selectedBranch, setSelectedBranch] = useState('');
    const [tripIds, setTripIds] = useState([]);
    const [docTypes, setDocTypes] = useState([]); // Store doc types from API
    const [saleDocuments, setSaleDocuments] = useState([]); // New state for sale documents
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [filteredData, setFilteredData] = useState([]);
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [fromDate, setFromDate] = useState('');
    const [toDate, setToDate] = useState('');
    const [dateRange, setDateRange] = useState({
        startDate: undefined,
        endDate: undefined,
    });
    const [tripDataLoaded, setTripDataLoaded] = useState(false); // Track if trip data is loaded
    const [lastTripSearchParams, setLastTripSearchParams] = useState(null); // Track last search params

    // Authentication states
    const [authToken, setAuthToken] = useState('');
    const [userData, setUserData] = useState(null);

    // Dropdown options
    const vehicleBranchOptions = ['Branch A - Vehicle 001', 'Branch B - Vehicle 002', 'Branch C - Vehicle 003'];

    // Load selected branch and auth data from AsyncStorage on component mount
    useEffect(() => {
        loadSelectedBranch();
        loadAuthData();
        // Set default dates: today and yesterday
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        
        setToDate(formatDateForDisplay(today));
        setFromDate(formatDateForDisplay(yesterday));
        
        setDateRange({
            startDate: yesterday,
            endDate: today,
        });
    }, []);


    const resetForm = () => {
        // Clear Vehicle Branch and reload it from AsyncStorage
        setSelectedVehicleBranch('');
        setSelectedBranch('');
        
        // Reload branch data from AsyncStorage
        loadSelectedBranch();
        
        setTripId('');
        setTripDetails('');
        setTripStart('');
        setTripEnd('');
        setSelectedDocType('');
        setSelectedDocTypeId('');
        setSelectedDocNo('');
        setSelectedDocData(null);
        setCustomerBranch('');
        setPlace('');
        setAddRemarks('');
        setWeight('');
        // Don't reset fromDate and toDate as they should remain as default dates
        // setFromDate(''); // REMOVED - preserve default dates
        // setToDate(''); // REMOVED - preserve default dates
        setTripIds([]);
        setSaleDocuments([]);
        setExpenseItems([]);
        setSelectedRows([]);
        // Don't reset dateRange as it should remain as default
        // setDateRange({ startDate: undefined, endDate: undefined }); // REMOVED
        setTripDataLoaded(false);
        setLastTripSearchParams(null);
    };

    // Load doc types when component mounts and token is available
    useEffect(() => {
        if (authToken) {
            fetchDocTypes();
        }
    }, [authToken]);

    // Filter data when search text changes
    useEffect(() => {
        if (searchText.trim() === '') {
            setFilteredData(modalData);
        } else {
            const filtered = modalData.filter(item => {
                if (typeof item === 'string') {
                    return item.toLowerCase().includes(searchText.toLowerCase());
                } else if (item.tripID) {
                    return item.tripID?.toLowerCase().includes(searchText.toLowerCase()) ||
                        item.vehicleNo?.toLowerCase().includes(searchText.toLowerCase());
                } else if (item.TransTypeName) {
                    return item.TransTypeName?.toLowerCase().includes(searchText.toLowerCase());
                } else if (item.DocName) { // New filter for sale documents
                    return item.DocName?.toLowerCase().includes(searchText.toLowerCase()) ||
                        item.DocID?.toLowerCase().includes(searchText.toLowerCase()) ||
                        item.POSCustomerID?.toLowerCase().includes(searchText.toLowerCase());
                }
                return false;
            });
            setFilteredData(filtered);
        }
    }, [searchText, modalData]);

    const loadAuthData = async () => {
        try {
            const token = await AsyncStorage.getItem('authToken');
            const userData = await AsyncStorage.getItem('userData');

            if (token) {
                setAuthToken(token);
            }
            if (userData) {
                setUserData(JSON.parse(userData));
            }
        } catch (error) {
            console.error('Error loading auth data:', error);
        }
    };

    const loadSelectedBranch = async () => {
        try {
            const branch = await AsyncStorage.getItem('selectedBranch');
            console.log('----Selected Branch --', branch);

            if (branch) {
                const branchData = JSON.parse(branch);
                setSelectedBranch(branchData.BranchId); // Use BranchId for API
                setSelectedVehicleBranch(branchData.BranchName); // Use BranchName for display
            }
        } catch (error) {
            console.error('Error loading selected branch:', error);
        }
    };

    const fetchDocTypes = async () => {
        if (!authToken) {
            console.error('No auth token available');
            return;
        }

        try {
            const response = await fetch('https://retailuat.abisibg.com/api/v1/doctype', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                if (response.status === 401) {
                    Alert.alert('Error', 'Authentication failed. Please login again.');
                    return;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            setDocTypes(Array.isArray(data) ? data : []);
        } catch (error) {
            console.error('Error fetching doc types:', error);
            Alert.alert('Error', 'Failed to fetch document types. Please try again.');
            setDocTypes([]);
        }
    };

    // New function to fetch sale documents
    const fetchSaleDocuments = async (branchId, fromDate, toDate) => {
        if (!branchId || !fromDate || !toDate) {
            Alert.alert('Error', 'Please select branch and date range');
            return;
        }

        if (!authToken) {
            Alert.alert('Error', 'Authentication token not found. Please login again.');
            return;
        }

        setLoading(true);
        try {
            const url = `https://retailuat.abisibg.com/api/v1/fetchsale?BranchId=${branchId}&stage=PL&FromDate=${fromDate}&ToDate=${toDate}`;
            console.log('Fetching sale documents from:', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                if (response.status === 401) {
                    Alert.alert('Error', 'Authentication failed. Please login again.');
                    return;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            setSaleDocuments(Array.isArray(data) ? data : []);
        } catch (error) {
            console.error('Error fetching sale documents:', error);
            Alert.alert('Error', 'Failed to fetch sale documents. Please try again.');
            setSaleDocuments([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchTripIds = async (branchId, fromDate, toDate) => {
        console.log('----', branchId, fromDate);

        if (!branchId || !fromDate || !toDate) {
            Alert.alert('Error', 'Please select branch and date range');
            return;
        }

        if (!authToken) {
            Alert.alert('Error', 'Authentication token not found. Please login again.');
            return;
        }

        // Check if we already have data for these exact parameters
        const currentParams = `${branchId}-${fromDate}-${toDate}`;
        if (tripDataLoaded && lastTripSearchParams === currentParams) {
            console.log('Trip data already loaded for these parameters, skipping fetch');
            return;
        }

        setLoading(true);
        try {
            const url = `https://retailUAT.abisaio.com:9001/api/Trip/${branchId}/${fromDate}/${toDate}`;
            console.log('----', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                if (response.status === 401) {
                    Alert.alert('Error', 'Authentication failed. Please login again.');
                    return;
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            setTripIds(Array.isArray(data) ? data : []);
            setTripDataLoaded(true);
            setLastTripSearchParams(currentParams);
        } catch (error) {
            console.error('Error fetching trip IDs:', error);
            Alert.alert('Error', 'Failed to fetch trip data. Please try again.');
            setTripIds([]);
        } finally {
            setLoading(false);
        }
    };

    const formatDateForAPI = (dateString) => {
        // Convert Date object to YYYYMMDD format
        if (dateString instanceof Date) {
            const year = dateString.getFullYear();
            const month = String(dateString.getMonth() + 1).padStart(2, '0');
            const day = String(dateString.getDate()).padStart(2, '0');
            return `${year}${month}${day}`;
        }
        // Convert YYYY-MM-DD to YYYYMMDD
        return dateString.replace(/-/g, '');
    };

    const formatDateForDisplay = (date) => {
        if (date instanceof Date) {
            return date.toISOString().split('T')[0]; // YYYY-MM-DD format
        }
        return date;
    };

    const handleDateRangeChange = (params) => {
        setDateRange({
            startDate: params.startDate,
            endDate: params.endDate,
        });
    };

    const confirmDateSelection = () => {
        if (dateRange.startDate) {
            setFromDate(formatDateForDisplay(dateRange.startDate));
        }
        if (dateRange.endDate) {
            setToDate(formatDateForDisplay(dateRange.endDate));
        }

        // Reset trip data loaded flag when dates change
        setTripDataLoaded(false);
        setLastTripSearchParams(null);

        setShowDatePicker(false);
    };

    const openDatePicker = () => {
        // Initialize date range with current values if they exist
        setDateRange({
            startDate: fromDate ? new Date(fromDate) : undefined,
            endDate: toDate ? new Date(toDate) : undefined,
        });
        setShowDatePicker(true);
    };

    const handleClear = () => {
        // Clear Vehicle Branch and reload it from AsyncStorage
        setSelectedVehicleBranch('');
        setSelectedBranch('');
        
        // Reload branch data from AsyncStorage
        loadSelectedBranch();
        
        setTripId('');
        setTripDetails('');
        setTripStart('');
        setTripEnd('');
        setSelectedDocType('');
        setSelectedDocTypeId('');
        setSelectedDocNo(''); // Clear selected doc number
        setSelectedDocData(null); // Clear selected doc data
        setCustomerBranch('');
        setPlace('');
        setAddRemarks('');
        setWeight('');
        // Don't reset fromDate and toDate as they should remain as default dates
        // setFromDate(''); // REMOVED - preserve default dates
        // setToDate(''); // REMOVED - preserve default dates
        setTripIds([]);
        setSaleDocuments([]); // Clear sale documents
        setExpenseItems([]); // Clear table items
        setSelectedRows([]); // Clear selected rows
    };

    const handleAdd = () => {
        if (!customerBranch) {
            Alert.alert('Validation Error', 'Please fill Customer/Branch field');
            return;
        }

        const newItem = {
            id: expenseItems.length + 1,
            lineNumber: (expenseItems.length + 1).toString(),
            itemName: customerBranch,
            nos: '1',
            kgs: weight || '0',
            remarks: addRemarks || '',
            place: place || '',
            docNo: selectedDocNo || '',
            transTypeID: selectedDocTypeId || '',
            customerID: selectedDocData?.CustomerID || '',
            addressID: selectedDocData?.DeliveryAddressID || '',
            transID: selectedDocData?.DocID || '',
            totalAmount: selectedDocData?.TotalAmount || 0,
            timeSlotId: selectedDocData?.TimeSlotId || ''
        };

        setExpenseItems([...expenseItems, newItem]);
        // Clear form after adding
        setCustomerBranch('');
        setPlace('');
        setAddRemarks('');
        setWeight('');
    };

// Updated saveTripDelivery function with correct tripTypeCode
const saveTripDelivery = async () => {
    if (!tripId || !selectedBranch || expenseItems.length === 0) {
        Alert.alert('Validation Error', 'Please fill Trip ID, Branch and add at least one item');
        return;
    }

    if (!authToken) {
        Alert.alert('Error', 'Authentication token not found. Please login again.');
        return;
    }

    setLoading(true);
    try {
        const tripDeliveryData = {
            tripID: tripId,
            branchId: selectedBranch,
            tripTypeCode: "DL", // Changed from "DELIVERY" to "DL"
            remarks: addRemarks || "",
            endReading: parseInt(tripEnd) || 0,
            posted: false,
            closed: false,
            closedOnBusinessDate: new Date().toISOString(),
            deleted: "N",
            createdUserId: userData?.UserId || "1000111",
            createdDate: new Date().toISOString(),
            modifiedUserId: "",
            modifiedDate: new Date().toISOString(),
            deletedUserId: "",
            deletedDate: new Date().toISOString(),
            tripDeliveryDtls: expenseItems.map((item) => ({
                lineNumber: item.lineNumber,
                transTypeID: item.transTypeID || selectedDocTypeId || "",
                docName: item.docNo || selectedDocNo || "",
                customerName: item.itemName || "",
                place: item.place || "",
                remarks: item.remarks || "",
                timeSlotId: item.timeSlotId || "",
                timeSlotName: "",
                kgweight: parseFloat(item.kgs) || 0,
                kmDistance: 0,
                amountCollected: item.totalAmount || 0,
                customerID: item.customerID || "",
                placeID: "",
                addressID: item.addressID || "",
                transID: item.transID || "",
                deliveryStatusCode: "PN",
                delApprovedBy: "",
                paymentGateWayId: "",
                paymentMethodId: "",
                paymentGatewayName: "",
                latitude: 0,
                longitude: 0,
                dlLatLongID: 0,
                daLatLongID: 0,
                refTransId: "",
                branchID: selectedBranch,
                deleted: "N",
                createdUserId: userData?.UserId || "1000111",
                createdDate: new Date().toISOString(),
                modifiedUserId: "",
                modifiedDate: new Date().toISOString(),
                deletedUserId: "",
                deletedDate: new Date().toISOString(),
                dml: "I"
            }))
        };

        console.log('Sending trip delivery data:', JSON.stringify(tripDeliveryData, null, 2));

        const response = await fetch('https://retailUAT.abisaio.com:9001/api/TripDelivery', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify(tripDeliveryData)
        });

        if (!response.ok) {
            if (response.status === 401) {
                Alert.alert('Error', 'Authentication failed. Please login again.');
                return;
            }
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
        }

        const result = await response.json();
        console.log('Trip delivery saved successfully:', result);
        
        Alert.alert(
            'Success', 
            'Trip delivery saved successfully!',
            [
                {
                    text: 'OK',
                    onPress: () => {
                        // Clear form after successful save
                        resetForm();
                    }
                }
            ]
        );

    } catch (error) {
        console.error('Error saving trip delivery:', error);
        Alert.alert('Error', `Failed to save trip delivery: ${error.message}`);
    } finally {
        setLoading(false);
    }
};

    const handleDeleteSelectedRows = () => {
        if (selectedRows.length === 0) {
            Alert.alert('Info', 'No rows selected for deletion');
            return;
        }

        const newItems = expenseItems.filter((_, index) => !selectedRows.includes(index));
        setExpenseItems(newItems);
        setSelectedRows([]);
    };

    const handleRowSelection = (index) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter(i => i !== index));
        } else {
            setSelectedRows([...selectedRows, index]);
        }
    };

    const openModal = (type) => {
        setSearchText('');

        switch (type) {
            case 'vehicleBranch':
                setModalData([selectedVehicleBranch || 'No branch selected']);
                break;
            case 'tripId':
                if (fromDate && toDate && selectedBranch) {
                    const formattedFromDate = formatDateForAPI(fromDate);
                    const formattedToDate = formatDateForAPI(toDate);
                    fetchTripIds(selectedBranch, formattedFromDate, formattedToDate);
                }
                setModalData(tripIds);
                break;
            case 'docType':
                setModalData(docTypes); // Use fetched doc types
                break;
            case 'docNo': // New case for document number selection
                if (fromDate && toDate && selectedBranch) {
                    const formattedFromDate = formatDateForAPI(fromDate);
                    const formattedToDate = formatDateForAPI(toDate);
                    fetchSaleDocuments(selectedBranch, formattedFromDate, formattedToDate);
                }
                setModalData(saleDocuments);
                break;
            default:
                setModalData([]);
        }
        setModalType(type);
        setModalVisible(true);
    };

    const handleModalItemSelect = (item) => {
        switch (modalType) {
            case 'vehicleBranch':
                setSelectedVehicleBranch(item);
                break;
            case 'tripId':
                if (typeof item === 'object' && item.tripID) {
                    setTripId(item.tripID);
                    setTripDetails(`Vehicle: ${item.vehicleNo}, Date: ${new Date(item.tripDate).toLocaleDateString()}`);
                    // Set the startReading in tripStart field
                    setTripStart(item.startReading ? item.startReading.toString() : '');
                    // Set the endReading in tripEnd field
                    setTripEnd(item.endReading ? item.endReading.toString() : '');
                } else {
                    setTripId(item);
                }
                break;
            case 'docType':
                if (typeof item === 'object' && item.TransTypeName) {
                    setSelectedDocType(item.TransTypeName); // Display TransTypeName
                    setSelectedDocTypeId(item.TransTypeId); // Store TransTypeId
                } else {
                    setSelectedDocType(item);
                }
                break;
            case 'docNo': // New case for document number selection
                if (typeof item === 'object' && item.DocID) {
                    setSelectedDocNo(item.DocID); // Set selected doc number
                    setSelectedDocData(item); // Store complete doc data
                    // Auto-populate customer and remarks from selected document
                    setCustomerBranch(item.POSCustomerID || '');
                    setAddRemarks(item.Remarks || '');
                } else {
                    setSelectedDocNo(item);
                }
                break;
        }
        setModalVisible(false);
    };



    const handleOptionPress = (option) => {
        setSelectedScrollOption(option);
        if (option === 'New') {
            resetForm();
        } else if (option === 'Save') {
            saveTripDelivery();
        } else if (option === 'View') {
            // Logic for view
        } else if (option === 'Cancel') {
            resetForm();
        }
    };


    const renderTripItem = ({ item }) => {
        if (typeof item === 'string') {
            return (
                <TouchableOpacity
                    style={styles.modalItem}
                    onPress={() => handleModalItemSelect(item)}
                >
                    <Text style={styles.modalItemText}>{item}</Text>
                </TouchableOpacity>
            );
        }

        // Handle trip items
        if (item.tripID) {
            return (
                <TouchableOpacity
                    style={styles.tripItemContainer}
                    onPress={() => handleModalItemSelect(item)}
                >
                    <Text style={styles.tripIdText}>{item.tripID}</Text>
                    <Text style={styles.tripDetailsText}>Vehicle: {item.vehicleNo}</Text>
                    <Text style={styles.tripDetailsText}>Date: {new Date(item.tripDate).toLocaleDateString()}</Text>
                    <Text style={styles.tripDetailsText}>Type: {item.tripTypeCode}</Text>
                    <Text style={styles.tripDetailsText}>Start Reading: {item.startReading}</Text>
                    <Text style={styles.tripDetailsText}>End Reading: {item.endReading}</Text>
                </TouchableOpacity>
            );
        }

        // Handle doc type items
        if (item.TransTypeName) {
            return (
                <TouchableOpacity
                    style={styles.modalItem}
                    onPress={() => handleModalItemSelect(item)}
                >
                    <Text style={styles.modalItemText}>{item.TransTypeName}</Text>
                    <Text style={styles.modalItemSubText}>ID: {item.TransTypeId}</Text>
                </TouchableOpacity>
            );
        }

        // Handle sale document items - New renderer
        if (item.DocID) {
            return (
                <TouchableOpacity
                    style={styles.saleDocItemContainer}
                    onPress={() => handleModalItemSelect(item)}
                >
                    <Text style={styles.docIdText}>{item.DocID}</Text>
                    <Text style={styles.docNameText}>{item.DocName}</Text>
                    <Text style={styles.docDetailsText}>Customer: {item.POSCustomerID}</Text>
                    <Text style={styles.docDetailsText}>Amount: ₹{item.TotalAmount}</Text>
                    <Text style={styles.docDetailsText}>Date: {new Date(item.BusinessDate).toLocaleDateString()}</Text>
                    {item.Remarks && <Text style={styles.docDetailsText}>Remarks: {item.Remarks}</Text>}
                </TouchableOpacity>
            );
        }

        return null;
    };

    return (
        <View style={styles.container}>
            <Navbar />
            <ScrollOptionsBar
                title="Trip Delivery"
                selectedOption={selectedScrollOption}
                onOptionPress={handleOptionPress}
            />
            {/* Header Navigation */}
            {/* <View style={styles.headerNav}>
                <TouchableOpacity style={styles.backButton}>
                    <Text style={styles.backButtonText}>← DELIVERY</Text>
                </TouchableOpacity>
                <View style={styles.headerButtons}>
                    <TouchableOpacity style={styles.newButton}>
                        <Text style={styles.newButtonText}>New ⌄</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.viewButton}>
                        <Text style={styles.viewButtonText}>View ●</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.saveButton} onPress={saveTripDelivery}>
                        <Text style={styles.saveButtonText}>Save</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.cancelButton}>
                        <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>
                </View>
            </View> */}

            <ScrollView style={styles.scrollContainer}>
                {/* Trip Details Section */}
                <View style={styles.sectionContainer}>
                    <Text style={styles.sectionTitle}>Trip Details</Text>

                    {/* Vehicle Branch Selection */}
                    <View style={styles.vehicleBranchRow}>
                        <TouchableOpacity
                            style={styles.vehicleBranchButton}
                            onPress={() => openModal('vehicleBranch')}
                        >
                            <Text style={[styles.vehicleBranchText, !selectedVehicleBranch && styles.placeholderText]}>
                                {selectedVehicleBranch || 'Select Vehicle Branch'}
                            </Text>
                        </TouchableOpacity>
                        {/* <TouchableOpacity style={styles.sendButton}>
                            <Text style={styles.sendButtonText}>Send</Text>
                        </TouchableOpacity> */}
                    </View>

                    {/* Trip ID Row */}
                    <View style={styles.tripIdRow}>
                        <TextInput
                            style={styles.tripIdInput}
                            value={tripId}
                            onChangeText={setTripId}
                            placeholder='Trip ID'
                            placeholderTextColor="#999"
                        />
                        <TouchableOpacity
                            style={styles.viewIdButton}
                            onPress={() => openModal('tripId')}
                        >
                            <Text style={styles.viewIdButtonText}>View</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.otherButton}>
                            <Text style={styles.otherButtonText}>Other</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Trip Details Text Area */}
                    <TextInput
                        style={styles.tripDetailsTextArea}
                        multiline
                        value={tripDetails}
                        placeholder='Trip Details'
                        placeholderTextColor="#999"
                        onChangeText={setTripDetails}
                    />

                    {/* Trip Start and End Row */}
                    <View style={styles.tripStartEndRow}>
                        <TextInput
                            style={styles.tripStartEndInput}
                            value={tripStart}
                            onChangeText={setTripStart}
                            placeholder='Trip Start'
                            placeholderTextColor="#999"
                        />
                        <TextInput
                            style={styles.tripStartEndInput}
                            value={tripEnd}
                            onChangeText={setTripEnd}
                            placeholder='Trip End'
                            placeholderTextColor="#999"
                        />
                    </View>

                    {/* DOC Type Row */}
                    <View style={styles.docTypeRow}>
                        <TouchableOpacity
                            style={styles.docTypeDropdown}
                            onPress={() => openModal('docType')}
                        >
                            <Text style={[styles.docTypeText, !selectedDocType && styles.placeholderText]}>
                                {selectedDocType || 'DOC Type'}
                            </Text>
                            <Text style={styles.dropdownIcon}>▼</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.chooseDocButton}
                            onPress={() => openModal('docNo')}
                        >
                            <Text style={[styles.chooseDocText, !selectedDocNo && styles.placeholderText]}>
                                {selectedDocNo || 'Choose DOC'}
                            </Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.docSendButton}>
                            <Text style={styles.docSendButtonText}>Send</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Customer/Branch and Place Row */}
                    <View style={styles.customerPlaceRow}>
                        <TextInput
                            style={styles.customerBranchInput}
                            value={customerBranch}
                            onChangeText={setCustomerBranch}
                            placeholder='Customer/Branch'
                            placeholderTextColor="#999"
                        />
                        <TextInput
                            style={styles.placeInput}
                            value={place}
                            onChangeText={setPlace}
                            placeholder='Place'
                            placeholderTextColor="#999"
                        />
                    </View>

                    {/* Add Remarks and Weight Row */}
                    <View style={styles.remarksWeightRow}>
                        <TextInput
                            style={styles.addRemarksInput}
                            value={addRemarks}
                            onChangeText={setAddRemarks}
                            placeholder='Add Remarks'
                            placeholderTextColor="#999"
                        />
                        <TextInput
                            style={styles.weightInput}
                            value={weight}
                            onChangeText={setWeight}
                            placeholder='Wt(Kg)'
                            placeholderTextColor="#999"
                            keyboardType="numeric"
                        />
                    </View>

                    {/* Action Buttons */}
                    <View style={styles.actionButtonsRow}>
                        <TouchableOpacity style={styles.addButton} onPress={handleAdd}>
                            <Text style={styles.addButtonText}>Add</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearButton} onPress={handleClear}>
                            <Text style={styles.clearButtonText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Table Section */}
                <View style={styles.tableContainer}>
                    <View style={styles.tableHeader}>
                        <View style={styles.checkboxCell}>
                            <CheckBox
                                value={false}
                                tintColors={{ true: '#007AFF', false: '#999' }}
                                style={styles.checkbox}
                            />
                        </View>
                        <View style={styles.lineNumberCell}>
                            <Text style={styles.headerText}>LineNumber</Text>
                        </View>
                        <View style={styles.itemNameCell}>
                            <Text style={styles.headerText}>Item Name</Text>
                        </View>
                        <View style={styles.nosCell}>
                            <Text style={styles.headerText}>Nos</Text>
                        </View>
                        <View style={styles.kgsCell}>
                            <Text style={styles.headerText}>Kgs</Text>
                        </View>
                        <View style={styles.remarksCell}>
                            <Text style={styles.headerText}>Remarks</Text>
                        </View>
                    </View>

                    {expenseItems.length > 0 ? (
                        expenseItems.map((item, index) => (
                            <View key={index} style={styles.tableRow}>
                                <View style={styles.checkboxCell}>
                                    <CheckBox
                                        value={selectedRows.includes(index)}
                                        onValueChange={() => handleRowSelection(index)}
                                        tintColors={{ true: '#007AFF', false: '#999' }}
                                        style={styles.checkbox}
                                    />
                                </View>
                                <View style={styles.lineNumberCell}>
                                    <Text style={styles.cellText}>{item.lineNumber}</Text>
                                </View>
                                <View style={styles.itemNameCell}>
                                    <Text style={styles.cellText}>{item.itemName}</Text>
                                </View>
                                <View style={styles.nosCell}>
                                    <Text style={styles.cellText}>{item.nos}</Text>
                                </View>
                                <View style={styles.kgsCell}>
                                    <Text style={styles.cellText}>{item.kgs}</Text>
                                </View>
                                <View style={styles.remarksCell}>
                                    <Text style={styles.cellText}>{item.remarks}</Text>
                                </View>
                            </View>
                        ))
                    ) : (
                        <View style={styles.emptyTableRow}>
                            <Text style={styles.emptyTableText}>No items added</Text>
                        </View>
                    )}
                </View>

                {/* Bottom Action Row */}
                <View style={styles.bottomActionRow}>
                    <TouchableOpacity style={styles.deleteSelectedButton} onPress={handleDeleteSelectedRows}>
                        <Text style={styles.deleteSelectedButtonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                    <View style={styles.weightButtonsContainer}>
                        <TouchableOpacity style={styles.netWeightButton}>
                            <Text style={styles.weightButtonText}>Net Weight</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.grossWeightButton}>
                            <Text style={styles.weightButtonText}>Gross Weight</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>

            {/* Date Range Picker Modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={showDatePicker}
                onRequestClose={() => setShowDatePicker(false)}
            >
                <View style={styles.datePickerModalOverlay}>
                    <View style={styles.datePickerModalContent}>
                        <Text style={styles.datePickerModalTitle}>
                            Select Date Range
                        </Text>

                        <View style={styles.datePickerInstructions}>
                            <Text style={styles.instructionText}>
                                Tap start date, then tap end date to select range
                            </Text>
                        </View>

                        <DatePicker
                            mode="range"
                            startDate={dateRange.startDate}
                            endDate={dateRange.endDate}
                            onChange={handleDateRangeChange}
                            styles={{
                                calendarContainer: {
                                    backgroundColor: 'white',
                                    borderRadius: 10,
                                },
                                headerContainer: {
                                    backgroundColor: '#f8f9fa',
                                    borderTopLeftRadius: 10,
                                    borderTopRightRadius: 10,
                                    paddingVertical: 15,
                                },
                                headerTitle: {
                                    color: '#333',
                                    fontSize: 18,
                                    fontWeight: 'bold',
                                },
                                weekDaysContainer: {
                                    backgroundColor: '#f1f3f4',
                                    paddingVertical: 10,
                                },
                                weekDay: {
                                    color: '#666',
                                    fontWeight: '600',
                                    fontSize: 14,
                                },
                                day: {
                                    width: 36,
                                    height: 36,
                                    borderRadius: 18,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    margin: 2,
                                },
                                today: {
                                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                                    borderWidth: 2,
                                    borderColor: '#007AFF',
                                    borderRadius: 18,
                                },
                                selected: {
                                    backgroundColor: '#87CEEB',
                                    borderRadius: 18,
                                },
                                selectedRange: {
                                    backgroundColor: 'rgba(135, 206, 250, 0.3)',
                                },
                                selectedText: {
                                    color: 'white',
                                    fontWeight: 'bold',
                                },
                                todayText: {
                                    color: '#007AFF',
                                    fontWeight: 'bold',
                                },
                                dayText: {
                                    color: '#333',
                                    fontSize: 14,
                                },
                                disabledDay: {
                                    opacity: 0.3,
                                },
                                disabledDayText: {
                                    color: '#ccc',
                                },
                            }}
                        />

                        <View style={styles.selectedRangeDisplay}>
                            <Text style={styles.selectedRangeLabel}>Selected Range:</Text>
                            <Text style={styles.selectedRangeText}>
                                {dateRange.startDate && dateRange.endDate
                                    ? `${formatDateForDisplay(dateRange.startDate)} to ${formatDateForDisplay(dateRange.endDate)}`
                                    : dateRange.startDate
                                        ? `From: ${formatDateForDisplay(dateRange.startDate)}`
                                        : 'No range selected'
                                }
                            </Text>
                        </View>

                        <View style={styles.datePickerButtons}>
                            <TouchableOpacity
                                style={styles.datePickerCancelButton}
                                onPress={() => setShowDatePicker(false)}
                            >
                                <Text style={styles.datePickerCancelButtonText}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.datePickerConfirmButton,
                                    (!dateRange.startDate || !dateRange.endDate) && styles.disabledButton
                                ]}
                                onPress={confirmDateSelection}
                                disabled={!dateRange.startDate || !dateRange.endDate}
                            >
                                <Text style={[
                                    styles.datePickerConfirmButtonText,
                                    (!dateRange.startDate || !dateRange.endDate) && styles.disabledButtonText
                                ]}>
                                    Confirm
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Selection Modal */}
            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>
                            {modalType === 'vehicleBranch' ? 'Select Vehicle Branch' :
                                modalType === 'tripId' ? 'Select Trip ID' :
                                    modalType === 'docType' ? 'Select Document Type' :
                                        modalType === 'docNo' ? 'Select Document Number' : 'Select Item'}
                        </Text>

                        {/* Search, Date Range and Filter Row */}
                        <View style={styles.searchDateRow}>
                            <TextInput
                                style={styles.searchInput}
                                placeholder="Search..."
                                value={searchText}
                                onChangeText={setSearchText}
                                placeholderTextColor="#999"
                            />
                        </View>

                        {(modalType === 'tripId' || modalType === 'docNo') && (
                            <View style={styles.dateRangeRow}>
                                <TouchableOpacity
                                    style={styles.dateRangeDisplay}
                                    onPress={openDatePicker}
                                >
                                    <View style={styles.dateRangeTextContainer}>
                                        <Text style={styles.dateRangeLabel}>Date Range:</Text>
                                        <Text style={[styles.dateRangeText, (!fromDate || !toDate) && styles.placeholderText]}>
                                            {fromDate && toDate
                                                ? `${fromDate} to ${toDate}`
                                                : 'Select date range'
                                            }
                                        </Text>
                                    </View>
                                    <Text style={styles.calendarIcon}>📅</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={styles.fetchButton}
                                    onPress={() => {
                                        if (fromDate && toDate && selectedBranch) {
                                            const formattedFromDate = formatDateForAPI(fromDate);
                                            const formattedToDate = formatDateForAPI(toDate);
                                            if (modalType === 'tripId') {
                                                fetchTripIds(selectedBranch, formattedFromDate, formattedToDate);
                                            } else if (modalType === 'docNo') {
                                                fetchSaleDocuments(selectedBranch, formattedFromDate, formattedToDate);
                                            }
                                        } else {
                                            Alert.alert('Error', 'Please select date range and branch');
                                        }
                                    }}
                                >
                                    <Text style={styles.fetchButtonText}>Fetch</Text>
                                </TouchableOpacity>
                            </View>
                        )}

                        {loading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator size="large" color="#007AFF" />
                                <Text style={styles.loadingText}>
                                    {modalType === 'tripId' ? 'Loading trips...' : 
                                     modalType === 'docNo' ? 'Loading documents...' : 'Loading...'}
                                </Text>
                            </View>
                        ) : (
                            <ScrollView style={styles.modalScrollView}>
                                {modalType === 'tripId' && tripIds.length > 0 ? (
                                    <View style={styles.modalItemsContainer}>
                                        {(filteredData.length > 0 ? filteredData : tripIds).map((item, index) => (
                                            <View key={index}>
                                                {renderTripItem({ item })}
                                            </View>
                                        ))}
                                    </View>
                                ) : modalType === 'docNo' && saleDocuments.length > 0 ? (
                                    <View style={styles.modalItemsContainer}>
                                        {(filteredData.length > 0 ? filteredData : saleDocuments).map((item, index) => (
                                            <View key={index}>
                                                {renderTripItem({ item })}
                                            </View>
                                        ))}
                                    </View>
                                ) : (
                                    <View style={styles.modalItemsContainer}>
                                        {(filteredData.length > 0 ? filteredData : modalData).map((item, index) => (
                                            <View key={index}>
                                                {renderTripItem({ item })}
                                            </View>
                                        ))}
                                    </View>
                                )}

                                {(filteredData.length === 0 && modalData.length === 0 && 
                                  (modalType === 'tripId' ? tripIds.length === 0 : true) &&
                                  (modalType === 'docNo' ? saleDocuments.length === 0 : true)) && (
                                    <Text style={styles.emptySearchText}>No items found</Text>
                                )}
                            </ScrollView>
                        )}

                        <TouchableOpacity
                            style={styles.modalCloseButton}
                            onPress={() => setModalVisible(false)}
                        >
                            <Text style={styles.modalCloseButtonText}>Close</Text>
                        </TouchableOpacity>
                    </View>
                </TouchableOpacity>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f0f0f0',
    },
    headerNav: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#aaa',
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    backButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },
    headerButtons: {
        flexDirection: 'row',
        gap: 10,
    },
    newButton: {
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 5,
        borderWidth: 1.5,
        borderColor: '#aaa',
    },
    newButtonText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
    },
    viewButton: {
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 5,
        borderWidth: 1.5,
        borderColor: '#aaa',
    },
    viewButtonText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '600',
    },
    saveButton: {
        backgroundColor: '#008000',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 5,
    },
    saveButtonText: {
        fontSize: 14,
        color: 'white',
        fontWeight: 'bold',
    },
    cancelButton: {
        backgroundColor: '#ff0000',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 5,
    },
    cancelButtonText: {
        fontSize: 14,
        color: 'white',
        fontWeight: 'bold',
    },
    scrollContainer: {
        flex: 1,
    },
    sectionContainer: {
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        padding: 18,
        margin: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: 22,
        fontWeight: '800',
        marginBottom: 15,
        color: '#333',
        textTransform: 'uppercase',
    },
    vehicleBranchRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    vehicleBranchButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        justifyContent: 'center',
        height: 48,
    },
    vehicleBranchText: {
        fontSize: 16,
        color: '#222',
        fontWeight: '600',
    },
    sendButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    sendButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    dateRangeContainer: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    dateButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        justifyContent: 'center',
        height: 48,
    },
    dateButtonText: {
        fontSize: 16,
        color: '#222',
        fontWeight: '600',
    },
    tripIdRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    tripIdInput: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    viewIdButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    viewIdButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    otherButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    otherButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    tripDetailsTextArea: {
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        padding: 12,
        height: 48,
        borderWidth: 1.5,
        borderColor: '#aaa',
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 15,
        textAlignVertical: 'top',
    },
    tripStartEndRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    tripStartEndInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    docTypeRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    docTypeDropdown: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
    },
    docTypeText: {
        color: '#222',
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    dropdownIcon: {
        color: '#222',
        fontSize: 16,
        fontWeight: 'bold',
    },
    chooseDocButton: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        justifyContent: 'center',
    },
    chooseDocText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#222',
    },
    docSendButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    docSendButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    customerPlaceRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerBranchInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    placeInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    remarksWeightRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    addRemarksInput: {
        flex: 2,
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        padding: 12,
        height: 70,
        textAlignVertical: 'top',
        borderWidth: 1.5,
        borderColor: '#aaa',
        fontSize: 16,
        fontWeight: '600',
    },
    weightInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    actionButtonsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 15,
    },
    addButton: {
        backgroundColor: '#008000',
        paddingVertical: 12,
        paddingHorizontal: 50,
        borderRadius: 8,
        alignItems: 'center',
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    clearButton: {
        backgroundColor: '#ff0000',
        paddingVertical: 12,
        paddingHorizontal: 50,
        borderRadius: 8,
        alignItems: 'center',
    },
    clearButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    placeholderText: {
        color: '#555',
        fontWeight: '500',
    },
    tableContainer: {
        margin: 10,
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        overflow: 'hidden',
        borderWidth: 1.5,
        borderColor: '#ccc',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#d3d3d3',
        borderBottomWidth: 2,
        borderBottomColor: '#999',
        padding: 8,
        height: 45,
    },
    headerText: {
        fontWeight: 'bold',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        padding: 8,
        height: 45,
    },
    cellText: {
        fontSize: 14,
        fontWeight: '600',
    },
    checkboxCell: {
        width: 35,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkbox: {
        transform: [{ scale: 1 }],
    },
    lineNumberCell: {
        width: 90,
        justifyContent: 'center',
        alignItems: 'center',
    },
    itemNameCell: {
        flex: 2,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    nosCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    kgsCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    remarksCell: {
        flex: 1.5,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    emptyTableRow: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableText: {
        color: '#777',
        fontSize: 18,
        fontWeight: '600',
    },
    bottomActionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: 10,
        marginTop: 15,
    },
    deleteSelectedButton: {
        backgroundColor: '#ff0000',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    deleteSelectedButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    weightButtonsContainer: {
        flexDirection: 'row',
        gap: 10,
    },
    netWeightButton: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1.5,
        borderColor: '#aaa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    grossWeightButton: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1.5,
        borderColor: '#aaa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    weightButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    datePickerModalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    datePickerModalContent: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    datePickerModalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#333',
    },
    datePickerInstructions: {
        backgroundColor: '#f0f8ff',
        padding: 10,
        borderRadius: 8,
        marginBottom: 15,
        borderLeftWidth: 4,
        borderLeftColor: '#007AFF',
    },
    instructionText: {
        fontSize: 14,
        color: '#007AFF',
        fontWeight: '500',
        textAlign: 'center',
    },
    selectedRangeDisplay: {
        backgroundColor: '#f8f9fa',
        padding: 15,
        borderRadius: 8,
        marginVertical: 15,
        borderWidth: 1,
        borderColor: '#e9ecef',
    },
    selectedRangeLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },
    selectedRangeText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#007AFF',
    },
    datePickerButtons: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 20,
        paddingTop: 15,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    datePickerCancelButton: {
        paddingVertical: 12,
        paddingHorizontal: 30,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        borderWidth: 1,
        borderColor: '#ddd',
    },
    datePickerCancelButtonText: {
        color: '#666',
        fontWeight: 'bold',
        fontSize: 16,
    },
    datePickerConfirmButton: {
        paddingVertical: 12,
        paddingHorizontal: 30,
        borderRadius: 8,
        backgroundColor: '#007AFF',
    },
    datePickerConfirmButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    disabledButton: {
        backgroundColor: '#ccc',
    },
    disabledButtonText: {
        color: '#999',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    searchDateRow: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        fontFamily: 'Poppins',
    },
    dateRangeRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    dateButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        justifyContent: 'center',
        height: 48,
    },
    dateButtonText: {
        fontSize: 16,
        color: '#222',
        fontWeight: '600',
    },
    tripIdRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    tripIdInput: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    viewIdButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    viewIdButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    otherButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    otherButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    tripDetailsTextArea: {
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        padding: 12,
        height: 48,
        borderWidth: 1.5,
        borderColor: '#aaa',
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 15,
        textAlignVertical: 'top',
    },
    tripStartEndRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    tripStartEndInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    docTypeRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    docTypeDropdown: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
    },
    docTypeText: {
        color: '#222',
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    dropdownIcon: {
        color: '#222',
        fontSize: 16,
        fontWeight: 'bold',
    },
    chooseDocButton: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        justifyContent: 'center',
    },
    chooseDocText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#222',
    },
    docSendButton: {
        backgroundColor: '#041C44',
        paddingHorizontal: 20,
        justifyContent: 'center',
        borderRadius: 5,
        height: 48,
    },
    docSendButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    customerPlaceRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    customerBranchInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    placeInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    remarksWeightRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 10,
    },
    addRemarksInput: {
        flex: 2,
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        padding: 12,
        height: 70,
        textAlignVertical: 'top',
        borderWidth: 1.5,
        borderColor: '#aaa',
        fontSize: 16,
        fontWeight: '600',
    },
    weightInput: {
        flex: 1,
        backgroundColor: '#e8e8e8',
        borderWidth: 1.5,
        borderColor: '#aaa',
        borderRadius: 5,
        padding: 12,
        height: 48,
        fontSize: 16,
        fontWeight: '600',
    },
    actionButtonsRow: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 15,
    },
    addButton: {
        backgroundColor: '#008000',
        paddingVertical: 12,
        paddingHorizontal: 50,
        borderRadius: 8,
        alignItems: 'center',
    },
    addButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    clearButton: {
        backgroundColor: '#ff0000',
        paddingVertical: 12,
        paddingHorizontal: 50,
        borderRadius: 8,
        alignItems: 'center',
    },
    clearButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    placeholderText: {
        color: '#555',
        fontWeight: '500',
    },
    tableContainer: {
        margin: 10,
        backgroundColor: '#e8e8e8',
        borderRadius: 5,
        overflow: 'hidden',
        borderWidth: 1.5,
        borderColor: '#ccc',
    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#d3d3d3',
        borderBottomWidth: 2,
        borderBottomColor: '#999',
        padding: 8,
        height: 45,
    },
    headerText: {
        fontWeight: 'bold',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
        padding: 8,
        height: 45,
    },
    cellText: {
        fontSize: 14,
        fontWeight: '600',
    },
    checkboxCell: {
        width: 35,
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkbox: {
        transform: [{ scale: 1 }],
    },
    lineNumberCell: {
        width: 90,
        justifyContent: 'center',
        alignItems: 'center',
    },
    itemNameCell: {
        flex: 2,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    nosCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    kgsCell: {
        width: 55,
        justifyContent: 'center',
        alignItems: 'center',
    },
    remarksCell: {
        flex: 1.5,
        justifyContent: 'center',
        paddingLeft: 8,
    },
    emptyTableRow: {
        height: 120,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyTableText: {
        color: '#777',
        fontSize: 18,
        fontWeight: '600',
    },
    bottomActionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: 10,
        marginTop: 15,
    },
    deleteSelectedButton: {
        backgroundColor: '#ff0000',
        paddingVertical: 12,
        paddingHorizontal: 25,
        borderRadius: 8,
    },
    deleteSelectedButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    weightButtonsContainer: {
        flexDirection: 'row',
        gap: 10,
    },
    netWeightButton: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1.5,
        borderColor: '#aaa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    grossWeightButton: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 8,
        alignItems: 'center',
        borderWidth: 1.5,
        borderColor: '#aaa',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    weightButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },

    // Calendar Modal Styles
    datePickerModalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    datePickerModalContent: {
        width: '90%',
        backgroundColor: 'white',
        borderRadius: 15,
        padding: 20,
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    datePickerModalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#333',
    },
    datePickerInstructions: {
        backgroundColor: '#f0f8ff',
        padding: 10,
        borderRadius: 8,
        marginBottom: 15,
        borderLeftWidth: 4,
        borderLeftColor: '#007AFF',
    },
    instructionText: {
        fontSize: 14,
        color: '#007AFF',
        fontWeight: '500',
        textAlign: 'center',
    },
    selectedRangeDisplay: {
        backgroundColor: '#f8f9fa',
        padding: 15,
        borderRadius: 8,
        marginVertical: 15,
        borderWidth: 1,
        borderColor: '#e9ecef',
    },
    selectedRangeLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#666',
        marginBottom: 5,
    },
    selectedRangeText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#007AFF',
    },
    datePickerButtons: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 20,
        paddingTop: 15,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    datePickerCancelButton: {
        paddingVertical: 12,
        paddingHorizontal: 30,
        borderRadius: 8,
        backgroundColor: '#f0f0f0',
        borderWidth: 1,
        borderColor: '#ddd',
    },
    datePickerCancelButtonText: {
        color: '#666',
        fontWeight: 'bold',
        fontSize: 16,
    },
    datePickerConfirmButton: {
        paddingVertical: 12,
        paddingHorizontal: 30,
        borderRadius: 8,
        backgroundColor: '#007AFF',
    },
    datePickerConfirmButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    disabledButton: {
        backgroundColor: '#ccc',
    },
    disabledButtonText: {
        color: '#999',
    },

    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        maxHeight: '80%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        borderWidth: 1,
        borderColor: '#ddd',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.3,
        shadowRadius: 5,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        textAlign: 'center',
        color: '#02096A',
        fontFamily: 'Poppins',
        paddingBottom: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },

    // Search and Date Row
    searchDateRow: {
        marginBottom: 15,
    },
    searchInput: {
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        fontFamily: 'Poppins',
    },

    // Date Range Row in Modal
    dateRangeRow: {
        flexDirection: 'row',
        marginBottom: 15,
        gap: 8,
        alignItems: 'center',
    },
    dateRangeDisplay: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: 50,
    },
    dateRangeTextContainer: {
        flex: 1,
    },
    dateRangeLabel: {
        fontSize: 12,
        color: '#666',
        fontWeight: '600',
        marginBottom: 2,
    },
    dateRangeText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
    },
    modalDateButton: {
        flex: 1,
        backgroundColor: 'white',
        borderWidth: 1.5,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: 45,
    },
    modalDateButtonText: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
    },
    calendarIcon: {
        fontSize: 18,
        marginLeft: 8,
    },
    fetchButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
        height: 50,
        justifyContent: 'center',
    },
    fetchButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    modalScrollView: {
        maxHeight: 400,
    },
    modalItemsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        marginBottom: 10,
    },
    modalItem: {
        backgroundColor: '#FDC500',
        width: 120,
        height: 120,
        padding: 5,
        borderRadius: 10,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        alignItems: 'center',
        justifyContent: 'center',
        margin: 5,
    },
    modalItemText: {
        fontSize: 15,
        color: '#333',
        fontWeight: '500',
        textAlign: 'center',
        fontFamily: 'Poppins',
    },
    modalItemSubText: {
        fontSize: 12,
        color: '#666',
        fontWeight: '400',
        textAlign: 'center',
        fontFamily: 'Poppins',
        marginTop: 2,
    },

    // Trip Item Container for API data
    tripItemContainer: {
        backgroundColor: '#f8f9fa',
        borderRadius: 8,
        padding: 15,
        marginVertical: 5,
        borderLeftWidth: 4,
        borderLeftColor: '#007AFF',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
    },
    tripIdText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 5,
    },
    tripDetailsText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 2,
    },

    // Sale Document Item Container - New styles
    saleDocItemContainer: {
        backgroundColor: '#fff8e1',
        borderRadius: 8,
        padding: 15,
        marginVertical: 5,
        borderLeftWidth: 4,
        borderLeftColor: '#FDC500',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
        minWidth: '100%',
    },
    docIdText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 5,
    },
    docNameText: {
        fontSize: 15,
        fontWeight: '600',
        color: '#555',
        marginBottom: 5,
    },
    docDetailsText: {
        fontSize: 14,
        color: '#666',
        marginBottom: 2,
    },

    // Loading styles
    loadingContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 40,
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
        color: '#666',
    },

    modalCloseButton: {
        marginTop: 15,
        alignSelf: 'flex-end',
        backgroundColor: '#02096A',
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 6,
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    modalCloseButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontFamily: 'Poppins',
    },
    emptySearchText: {
        textAlign: 'center',
        fontFamily: 'Poppins',
        color: '#666',
        marginTop: 20,
        marginBottom: 20,
        fontStyle: 'italic',
    },
});

export default TripDelivery;